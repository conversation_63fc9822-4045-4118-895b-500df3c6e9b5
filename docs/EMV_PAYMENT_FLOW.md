# EMV Payment Flow Implementation

## Overview

This implementation adds support for EMV payment processing by showing different screens based on whether an EMV device is configured or not.

## Flow Logic

### 1. Payment Initiation
When a user initiates payment from the cart screen:

1. `CartProvider.processStripePayment()` is called
2. This calls `PaymentGatewayService.startTakeawayPayment()`
3. The service checks if an EMV device is configured by looking for `selected_emv` in SharedPreferences

### 2. Screen Selection
Based on EMV setup:

- **EMV Configured**: Navigate to `/emv-payment` (EmvPaymentScreen)
- **No EMV**: Navigate to `/payment-webview` (PaymentWebViewScreen)

### 3. EMV Payment Screen Features

#### Visual Elements
- Payment amount display
- EMV device information (if available)
- Animated card icon with pulsing effect
- Status messages
- Cancel button
- Test simulation button (for development)

#### Functionality
- Polls payment status every 2 seconds
- Handles payment success/failure
- Provides visual feedback during processing
- Supports cancellation
- On success: Navigates directly to order confirmation screen
- Order confirmation screen handles cart clearing and bill printing

## Files Modified/Created

### New Files
- `lib/screens/payments/emv_payment_screen.dart` - Main EMV payment UI
- `docs/EMV_PAYMENT_FLOW.md` - This documentation

### Modified Files
- `lib/services/payment_gateway_service.dart` - Added EMV detection logic
- `lib/router/app_router.dart` - Added EMV payment route

## Key Components

### EmvPaymentScreen
```dart
class EmvPaymentScreen extends StatefulWidget {
  final PaymentRequestDetails paymentDetails;
  final Restaurant restaurant;
  final Order order;
}
```

**Features:**
- Animated UI with pulsing card icon
- EMV device information display
- Payment status polling
- Success/failure handling
- Test simulation capability

### PaymentGatewayService Updates
```dart
// Check if EMV is set up
final selectedEmvJson = sharedPreferences.getString("selected_emv");
final hasEmvSetup = selectedEmvJson != null;

// Navigate to appropriate screen
String route = hasEmvSetup ? '/emv-payment' : '/payment-webview';
```

## Usage

### For Users
1. Set up EMV device in Settings → EMV Setup
2. Add items to cart
3. Click "Checkout"
4. If EMV is configured: See "Tap or Swipe Card" screen
5. If no EMV: See regular payment webview
6. On successful EMV payment: Automatically navigate to order confirmation screen

### For Developers
- Use the "Simulate Card Tap (Test)" button for testing
- Check debug logs for payment flow information
- EMV device selection is stored in SharedPreferences as `selected_emv`

## Testing

### Manual Testing
1. **Without EMV**: Clear `selected_emv` from SharedPreferences → Should show webview
2. **With EMV**: Set up an EMV device → Should show EMV payment screen
3. **Simulation**: Use test button to simulate successful payment

### Debug Information
The implementation includes extensive debug logging:
- EMV detection status
- Route selection logic
- Payment status polling
- Success/failure handling

## Success Flow

### EMV Payment Success
1. EMV payment screen detects successful payment
2. Shows "Payment successful!" message for 2 seconds
3. Navigates directly to `/order-confirmation` screen
4. Order confirmation screen automatically:
   - Prints the bill (if printer is configured)
   - Clears the cart
   - Shows success animation and order details

### Regular Payment Success
1. Payment webview returns 'success' to PaymentGatewayService
2. PaymentGatewayService calls onSuccess callback
3. CartProvider handles success by clearing cart and navigating to order confirmation

## Integration Points

### EMV Device Setup
- EMV devices are configured in `SetupEmvScreen`
- Selected device is stored in SharedPreferences
- Device information is displayed on payment screen

### Payment Processing
- Uses existing Stripe payment infrastructure
- Supports both EMV and online payment methods
- Maintains compatibility with existing payment verification

## Future Enhancements

1. **Real EMV Integration**: Replace simulation with actual EMV SDK calls
2. **Payment Status API**: Implement real-time payment status checking
3. **Error Handling**: Add more specific error messages for different failure scenarios
4. **Accessibility**: Add screen reader support and keyboard navigation
5. **Customization**: Allow theming and branding customization

## Configuration

### EMV Setup
Users can configure EMV devices through:
- Settings → EMV Setup
- Device selection and status checking
- Automatic device discovery

### Payment Gateway
- Requires Stripe payment gateway configuration
- Supports both EMV and online payment methods
- Maintains existing payment verification flow

## Security Considerations

- EMV device communication should be encrypted
- Payment data should never be stored locally
- Use secure communication channels for payment status
- Implement proper session management
- Follow PCI DSS compliance requirements

## Troubleshooting

### Common Issues
1. **EMV screen not showing**: Check if EMV device is properly configured
2. **Payment stuck**: Verify backend payment processing
3. **Navigation issues**: Ensure proper route configuration

### Debug Steps
1. Check SharedPreferences for `selected_emv`
2. Verify payment gateway configuration
3. Review debug logs for flow information
4. Test with simulation button first
