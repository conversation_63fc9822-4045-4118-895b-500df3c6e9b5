import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../common/constants/colors.dart';
import '../core/utils.dart';
import '../models/add_on_item.dart';
import '../models/food/food_item.dart';
import '../models/order/order_addon.dart';
import '../providers/cart_provider.dart';
import '../providers/data_provider.dart';

class AddOnPopup extends StatefulWidget {
  final FoodItem foodItem;
  final List<AddOn> addOnGroups;
  final CartProvider cartProvider;
  final Function(String)? onStateChange; // Changed from onItemAdded

  const AddOnPopup({
    required this.foodItem,
    required this.addOnGroups,
    required this.cartProvider,
    this.onStateChange, // Changed from onItemAdded
    super.key,
  });

  @override
  State<AddOnPopup> createState() => _AddOnPopupState();
}

class _AddOnPopupState extends State<AddOnPopup> with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  Map<int, Set<String>> _selectedOptions = {};
  double _currentTotalAddOnPrice = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeSelections();
    _calculateTotal();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
        .animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutQuart),
        );
    _slideController.forward();
  }

  void _initializeSelections() {
    _selectedOptions = {};
    for (var group in widget.addOnGroups) {
      final selectionsInGroup = <String>{};

      // First, add all mustSelected items
      for (var delegate in group.items) {
        if (delegate.mustSelected) {
          selectionsInGroup.add(delegate.refId);
        }
      }

      // Then, add defaultSelected items if there's room and they're not already added
      for (var delegate in group.items) {
        if (delegate.defaultSelected &&
            !selectionsInGroup.contains(delegate.refId)) {
          // Check if we can add more items based on selection limit
          if (group.selectionLimit == 0 ||
              selectionsInGroup.length < group.selectionLimit) {
            selectionsInGroup.add(delegate.refId);
          }
        }
      }

      // For single-select groups, ensure only one item is selected
      if (!group.multiSelect && selectionsInGroup.length > 1) {
        // Keep only the first mustSelected item, or first defaultSelected if no mustSelected
        String? keepItem;
        for (var delegate in group.items) {
          if (delegate.mustSelected &&
              selectionsInGroup.contains(delegate.refId)) {
            keepItem = delegate.refId;
            break;
          }
        }
        if (keepItem == null) {
          for (var delegate in group.items) {
            if (delegate.defaultSelected &&
                selectionsInGroup.contains(delegate.refId)) {
              keepItem = delegate.refId;
              break;
            }
          }
        }
        selectionsInGroup.clear();
        if (keepItem != null) {
          selectionsInGroup.add(keepItem);
        }
      }

      _selectedOptions[group.id] = selectionsInGroup;
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  void _toggleOption(AddOn group, AddOnDelegate delegate) {
    setState(() {
      final selectionsInGroup = _selectedOptions.putIfAbsent(
        group.id,
        () => <String>{},
      );
      final delegateId = delegate.refId;

      if (group.multiSelect) {
        if (selectionsInGroup.contains(delegateId)) {
          // Can only remove if not mustSelected
          if (!delegate.mustSelected) {
            selectionsInGroup.remove(delegateId);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  '${delegate.getItem(Provider.of<DataProvider>(context, listen: false)).name} is required and cannot be removed',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
        } else {
          // Adding item
          if (group.selectionLimit == 0 ||
              selectionsInGroup.length < group.selectionLimit) {
            selectionsInGroup.add(delegateId);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Maximum ${group.selectionLimit} selection(s) allowed for ${group.groupName}',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // Single select
        if (selectionsInGroup.contains(delegateId)) {
          // Can only remove if not mustSelected
          if (!delegate.mustSelected) {
            selectionsInGroup.remove(delegateId);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  '${delegate.getItem(Provider.of<DataProvider>(context, listen: false)).name} is required and cannot be removed',
                ),
                backgroundColor: Colors.orange,
              ),
            );
          }
        } else {
          // For single select, clear others and add this one
          selectionsInGroup.clear();
          selectionsInGroup.add(delegateId);
        }
      }
      _selectedOptions[group.id] = selectionsInGroup;
      _calculateTotal();
    });
  }

  void _calculateTotal() {
    double total = 0;
    _selectedOptions.forEach((groupId, delegateRefs) {
      final group = widget.addOnGroups.firstWhere((g) => g.id == groupId);
      for (var delegateRef in delegateRefs) {
        final delegate = group.items.firstWhere(
          (item) => item.refId == delegateRef,
        );
        total += delegate.price;
      }
    });
    setState(() {
      _currentTotalAddOnPrice = total;
    });
  }

  List<OrderAddOn> _getSelectedOrderAddOns() {
    final List<OrderAddOn> orderAddOns = [];
    _selectedOptions.forEach((groupId, delegateRefs) {
      final group = widget.addOnGroups.firstWhere((g) => g.id == groupId);
      for (var delegateRef in delegateRefs) {
        final delegate = group.items.firstWhere((d) => d.refId == delegateRef);
        orderAddOns.add(
          OrderAddOn.create(
            refId: delegate.refId,
            price: delegate.price,
            quantity: 1,
            defaultSelected: delegate.defaultSelected,
            mustSelected: delegate.mustSelected,
          ),
        );
      }
    });
    return orderAddOns;
  }

  bool _validateMustSelectedItems() {
    for (var group in widget.addOnGroups) {
      final selectionsInGroup = _selectedOptions[group.id] ?? <String>{};

      // Check if all mustSelected items in this group are selected
      for (var delegate in group.items) {
        if (delegate.mustSelected &&
            !selectionsInGroup.contains(delegate.refId)) {
          return false;
        }
      }
    }
    return true;
  }

  void _addToCartWithAddOns() {
    if (!mounted) return;

    // Validate that all must-selected items are selected
    if (!_validateMustSelectedItems()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'Please select all required items before adding to cart',
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final selectedOrderAddons = _getSelectedOrderAddOns();

    // Convert OrderAddOn to AddOnDelegate
    List<AddOnDelegate> selectedAddOnDelegates = [];
    for (var orderAddon in selectedOrderAddons) {
      for (var group in widget.addOnGroups) {
        try {
          AddOnDelegate delegate = group.items.firstWhere(
            (d) => d.refId == orderAddon.refId,
          );
          selectedAddOnDelegates.add(delegate);
          break;
        } catch (e) {
          /* continue */
        }
      }
    }

    // Add item with addons to cart properly
    widget.cartProvider.addItemWithAddons(
      widget.foodItem,
      selectedAddOnDelegates,
    );

    Navigator.of(context).pop();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onStateChange?.call("itemAdded");
    });
  }

  void _skipAddOns() {
    if (!mounted) return;

    // Collect all must-selected addons
    final List<AddOnDelegate> mustHaveAddOnDelegates = [];
    for (var group in widget.addOnGroups) {
      for (var delegate in group.items) {
        if (delegate.mustSelected) {
          mustHaveAddOnDelegates.add(delegate);
        }
      }
    }

    // Add item with must-have addons to cart properly
    widget.cartProvider.addItemWithAddons(
      widget.foodItem,
      mustHaveAddOnDelegates,
    );

    Navigator.of(context).pop();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onStateChange?.call("itemAddedViaSkip");
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(20),
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
            maxWidth: 500,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              _buildMainItemDisplay(),
              Flexible(child: _buildAddOnGroupList()),
              _buildBottomSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.primary.withAlpha(10),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Row(
        children: [
          Icon(Icons.add_circle_outline, color: AppColors.primary, size: 28),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Add Extras',
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                Text(
                  'Customize your ${widget.foodItem.name}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onStateChange?.call(
                "popupDismissed",
              ); // Notify about dismissal
            },
            icon: Icon(Icons.close, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildMainItemDisplay() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withAlpha(5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withAlpha(20)),
      ),
      child: Row(
        children: [
          Icon(Icons.restaurant_menu, color: AppColors.primary, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.foodItem.name,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  formatCurrencyWithRestaurant(
                    widget.foodItem.price,
                    Provider.of<DataProvider>(
                          context,
                          listen: false,
                        ).hasRestaurant
                        ? Provider.of<DataProvider>(
                            context,
                            listen: false,
                          ).restaurant.restaurantSettings.currencyValue
                        : null,
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'Included',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddOnGroupList() {
    if (widget.addOnGroups.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            "No add-ons available for this item.",
            style: TextStyle(color: AppColors.textSecondary),
          ),
        ),
      );
    }
    return ListView.builder(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(horizontal: 0),
      itemCount: widget.addOnGroups.length,
      itemBuilder: (context, index) {
        final group = widget.addOnGroups[index];
        return _buildAddOnGroup(group);
      },
    );
  }

  Widget _buildAddOnGroup(AddOn group) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            group.groupName,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          if (group.selectionLimit > 0)
            Text(
              group.multiSelect
                  ? "Select up to ${group.selectionLimit}"
                  : "Select 1",
              style: TextStyle(fontSize: 12, color: AppColors.textSecondary),
            ),
          const SizedBox(height: 8),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: group.items.length,
            itemBuilder: (context, delegateIndex) {
              final delegate = group.items.toList()[delegateIndex];
              return _buildAddOnDelegateItem(group, delegate);
            },
          ),
          if (widget.addOnGroups.indexOf(group) < widget.addOnGroups.length - 1)
            const Divider(height: 24, thickness: 1),
        ],
      ),
    );
  }

  Widget _buildAddOnDelegateItem(AddOn group, AddOnDelegate delegate) {
    final bool isSelected =
        _selectedOptions[group.id]?.contains(delegate.refId) ?? false;
    final bool canToggle = !(isSelected && delegate.mustSelected);
    final DataProvider dataProvider = Provider.of<DataProvider>(
      context,
      listen: false,
    );

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary.withAlpha(10) : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppColors.primary : Colors.grey[300]!,
          width: isSelected ? 1.5 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: canToggle ? () => _toggleOption(group, delegate) : null,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.extension,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        delegate.getItem(dataProvider).name,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  (delegate.price > 0
                      ? '+${formatCurrencyWithRestaurant(delegate.price, dataProvider.hasRestaurant ? dataProvider.restaurant.restaurantSettings.currencyValue : null)}'
                      : 'Included'),
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                    color: delegate.price > 0
                        ? AppColors.primary
                        : AppColors.textSecondary,
                  ),
                ),
                const SizedBox(width: 12),
                group.multiSelect
                    ? Checkbox(
                        value: isSelected,
                        onChanged: canToggle
                            ? (bool? value) => _toggleOption(group, delegate)
                            : null,
                        activeColor: AppColors.primary,
                        visualDensity: VisualDensity.compact,
                        side: BorderSide(color: Colors.grey[400]!, width: 2),
                      )
                    : GestureDetector(
                        onTap: canToggle
                            ? () => _toggleOption(group, delegate)
                            : null,
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isSelected
                                  ? AppColors.primary
                                  : Colors.grey[400]!,
                              width: 2,
                            ),
                            color: isSelected
                                ? AppColors.primary
                                : Colors.transparent,
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.circle,
                                  size: 12,
                                  color: Colors.white,
                                )
                              : null,
                        ),
                      ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    final double grandTotal = widget.foodItem.price + _currentTotalAddOnPrice;
    bool hasSelections =
        _currentTotalAddOnPrice > 0 ||
        _selectedOptions.values.any((set) => set.isNotEmpty);

    final DataProvider dataProvider = Provider.of<DataProvider>(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(24)),
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            margin: const EdgeInsets.only(bottom: 16),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(10),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Main item:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      formatCurrencyWithRestaurant(
                        widget.foodItem.price,
                        dataProvider.hasRestaurant
                            ? dataProvider
                                  .restaurant
                                  .restaurantSettings
                                  .currencyValue
                            : null,
                      ),
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
                if (_currentTotalAddOnPrice > 0 || hasSelections) ...[
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Add-ons:',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.textSecondary,
                        ),
                      ),
                      Text(
                        '+${formatCurrencyWithRestaurant(_currentTotalAddOnPrice, dataProvider.hasRestaurant ? dataProvider.restaurant.restaurantSettings.currencyValue : null)}',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ],
                  ),
                  Divider(
                    height: 16,
                    thickness: 1,
                    color: AppColors.primary.withAlpha(20),
                  ),
                ],
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Total:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    Text(
                      formatCurrencyWithRestaurant(
                        grandTotal,
                        dataProvider.hasRestaurant
                            ? dataProvider
                                  .restaurant
                                  .restaurantSettings
                                  .currencyValue
                            : null,
                      ),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: _skipAddOns,
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    'Skip Extras',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _addToCartWithAddOns,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    hasSelections ? 'Add with Extras' : 'Add to Cart',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Helper to show the popup
Future<void> showAddOnPopup(
  BuildContext context,
  FoodItem foodItem,
  CartProvider cartProvider,
  Function(String)?
  onStateChangeCallback, // Changed from VoidCallback onItemAdded
) async {
  final List<AddOn> addonGroups = foodItem.addons.toList();

  return showDialog(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext dialogContext) {
      return AddOnPopup(
        foodItem: foodItem,
        addOnGroups: addonGroups,
        cartProvider: cartProvider,
        onStateChange: onStateChangeCallback, // Changed from onItemAdded
      );
    },
  );
}
