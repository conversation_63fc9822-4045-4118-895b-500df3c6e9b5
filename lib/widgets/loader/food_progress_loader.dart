import 'package:flutter/material.dart';

class FoodProgressLoader extends StatefulWidget {
  @override
  _FoodProgressLoaderState createState() => _FoodProgressLoaderState();
}

class _FoodProgressLoaderState extends State<FoodProgressLoader>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    _progressAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: 200,
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.grey.shade200,
          ),
          child: Stack(
            children: [
              Container(
                width: 200 * _progressAnimation.value,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade300, Colors.red.shade400],
                  ),
                ),
              ),
              Positioned(
                left: (200 * _progressAnimation.value) - 12,
                top: -8,
                child: const Icon(
                  Icons.restaurant,
                  size: 24,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}