import 'package:flutter/material.dart';


class PizzaSliceLoader extends StatefulWidget {
  @override
  _PizzaSliceLoaderState createState() => _PizzaSliceLoaderState();
}

class _PizzaSliceLoaderState extends State<PizzaSliceLoader>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _rotationAnimation = Tween<double>(begin: 0, end: 2 * 3.14159).animate(
      CurvedAnimation(parent: _controller, curve: Curves.linear),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationAnimation.value,
          child: CustomPaint(
            size: const Size(50, 50),
            painter: <PERSON>SlicePainter(),
          ),
        );
      },
    );
  }
}

class PizzaSlicePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2;

    // Pizza base
    final basePaint = Paint()
      ..color = Colors.orange.shade300
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, radius, basePaint);

    // Pizza toppings (pepperoni)
    final toppingPaint = Paint()
      ..color = Colors.red.shade400
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(center.dx - 8, center.dy - 8), 4, toppingPaint);
    canvas.drawCircle(Offset(center.dx + 6, center.dy - 6), 3, toppingPaint);
    canvas.drawCircle(Offset(center.dx - 4, center.dy + 8), 3, toppingPaint);

    // Missing slice
    final slicePaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -0.3, // start angle
      0.6, // sweep angle
      true,
      slicePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}