import 'dart:math';

import 'package:flutter/material.dart';

import 'loader/loader.dart';

class LoadingDialog {
  final BuildContext context;
  final String message;
  late OverlayEntry _overlayEntry;
  bool _isShowing = false;

  LoadingDialog(this.context, this.message);

  void show() {
    if (_isShowing) return;
    
    _overlayEntry = OverlayEntry(
      builder: (context) => _LoadingDialogWidget(message: message),
    );
    
    Overlay.of(context).insert(_overlayEntry);
    _isShowing = true;
  }

  void dismiss() {
    if (!_isShowing) return;
    
    _overlayEntry.remove();
    _isShowing = false;
  }
}
enum LoadingAnimationType {
  pulsingFood,
  cookingPan,
  pizzaSlice,
  bouncingDots,
  foodProgress,
}

class _LoadingDialogWidget extends StatefulWidget {
  final String message;
  final LoadingAnimationType animationType;

  const _LoadingDialogWidget({
    required this.message,
    this.animationType = LoadingAnimationType.pulsingFood,
  });

  @override
  State<_LoadingDialogWidget> createState() => _LoadingDialogWidgetState();
}

class _LoadingDialogWidgetState extends State<_LoadingDialogWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late LoadingAnimationType _selectedAnimationType;

  @override
  void initState() {
    super.initState();
    _selectedAnimationType = LoadingAnimationType.values[
    Random().nextInt(LoadingAnimationType.values.length)];

    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _buildLoadingAnimation() {
    switch (_selectedAnimationType) {
      case LoadingAnimationType.pulsingFood:
        return PulsingFoodLoader();
      case LoadingAnimationType.cookingPan:
        return CookingPanLoader();
      case LoadingAnimationType.pizzaSlice:
        return PizzaSliceLoader();
      case LoadingAnimationType.bouncingDots:
        return BouncingDotsLoader();
      case LoadingAnimationType.foodProgress:
        return FoodProgressLoader();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: Center(
        child: ScaleTransition(
          scale: _animation,
          child: Container(
            padding: const EdgeInsets.all(32),
            margin: const EdgeInsets.symmetric(horizontal: 40),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  height: 60,
                  child: _buildLoadingAnimation(),
                ),
                const SizedBox(height: 24),
                Text(
                  widget.message,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}