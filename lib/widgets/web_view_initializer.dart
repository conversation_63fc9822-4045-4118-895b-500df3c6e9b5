import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:path_provider/path_provider.dart';

class WebViewInitializer {
  static WebViewEnvironment? _webViewEnvironment;

  static Future<void> init() async {
    if (Platform.isWindows && _webViewEnvironment == null) {
      try {
        final appDir = await getApplicationSupportDirectory();
        final udfPath = '${appDir.path}\\WebView2';
        final udfDir = Directory(udfPath);
        if (!await udfDir.exists()) {
          await udfDir.create(recursive: true);
          debugPrint('Main: Created WebView2 UDF: $udfPath');
        }
        _webViewEnvironment = await WebViewEnvironment.create(
          settings: WebViewEnvironmentSettings(userDataFolder: udfPath),
        );
        debugPrint('Main: WebViewEnvironment initialized successfully');
      } catch (e) {
        debugPrint('Main: Failed to initialize WebViewEnvironment: $e');
        // Fallback to a safe directory
        const fallbackUdfPath = r'C:\Temp\DineazyWebView2';
        final fallbackDir = Directory(fallbackUdfPath);
        if (!await fallbackDir.exists()) {
          await fallbackDir.create(recursive: true);
        }
        _webViewEnvironment = await WebViewEnvironment.create(
          settings: WebViewEnvironmentSettings(userDataFolder: fallbackUdfPath),
        );
        debugPrint('Main: Using fallback WebView2 UDF: $fallbackUdfPath');
      }
    }
  }

  static WebViewEnvironment? get environment => _webViewEnvironment;
}

