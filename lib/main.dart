import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'common/globals.dart';
import 'common/theme/app_theme.dart';
import 'providers/auth_provider.dart';
import 'providers/cart_provider.dart';
import 'providers/data_provider.dart';
import 'router//app_router.dart';
import 'widgets/web_view_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await WebViewInitializer.init();

  sharedPreferences = await SharedPreferences.getInstance();

  String initialRoute = '/';
  final authProvider = AuthProvider();
  if (authProvider.hasValidSession()) {
    initialRoute = '/home';
  }

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => CartProvider()),
        ChangeNotifierProvider(create: (_) => DataProvider()),
        ChangeNotifierProvider.value(value: authProvider),
      ],
      child: MyApp(initialRoute: initialRoute),
    ),
  );
}

class MyApp extends StatefulWidget {
  final String initialRoute;

  const MyApp({super.key, required this.initialRoute});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    final router = getRouterConfig(widget.initialRoute);

    return MaterialApp.router(
      title: 'Dineazy Kiosk',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.theme,
      routerConfig: router,
    );
  }
}
