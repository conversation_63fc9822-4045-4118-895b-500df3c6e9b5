import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../common/constants/colors.dart';
import '../../common/globals.dart';
import '../../models/emv.dart';
import '../../models/order/order.dart';
import '../../models/payment_request_details.dart';
import '../../models/restaurant/restaurant.dart';
import '../../services/payment_gateway_service.dart';
import '../../widgets/loading_dialog.dart';

class EmvPaymentScreen extends StatefulWidget {
  final PaymentRequestDetails paymentDetails;
  final Restaurant restaurant;
  final Order order;

  const EmvPaymentScreen({super.key, required this.paymentDetails, required this.restaurant, required this.order});

  @override
  State<EmvPaymentScreen> createState() => _EmvPaymentScreenState();
}

class _EmvPaymentScreenState extends State<EmvPaymentScreen> with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _cardController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _cardAnimation;

  Emv? selectedEmv;
  bool isProcessing = false;
  String statusMessage = 'Please tap or swipe your card';
  late PaymentGatewayService paymentService;
  LoadingDialog? loadingDialog;
  Timer? _paymentStatusTimer;

  @override
  void initState() {
    super.initState();
    paymentService = PaymentGatewayService(context);
    _loadSelectedEmv();
    _initializeAnimations();
    _startPaymentStatusPolling();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(duration: const Duration(seconds: 2), vsync: this);
    _cardController = AnimationController(duration: const Duration(milliseconds: 800), vsync: this);

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut));

    _cardAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _cardController, curve: Curves.elasticOut));

    _pulseController.repeat(reverse: true);
    _cardController.forward();
  }

  void _loadSelectedEmv() {
    final selectedEmvJson = sharedPreferences.getString("selected_emv");
    if (selectedEmvJson != null) {
      try {
        selectedEmv = Emv(jsonDecode(selectedEmvJson));
        debugPrint('EmvPaymentScreen: Loaded EMV: ${selectedEmv?.label}');
      } catch (e) {
        debugPrint('EmvPaymentScreen: Error loading EMV: $e');
      }
    }
  }

  void _startPaymentStatusPolling() {
    _paymentStatusTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      _checkPaymentStatus();
    });
  }

  Future<void> _checkPaymentStatus() async {
    if (!mounted || isProcessing) return;

    try {
      debugPrint('EmvPaymentScreen: Checking payment status...');
      final isVerified = await paymentService.checkPaymentStatus(widget.paymentDetails.id);

      if (isVerified) {
        _handlePaymentSuccess();
        return;
      }
    } catch (e) {
      debugPrint('EmvPaymentScreen: Error checking payment status: $e');
      _handlePaymentError(e.toString());
    }
  }

  void _handlePaymentSuccess() {
    setState(() {
      isProcessing = false;
      statusMessage = 'Payment successful!';
    });

    _pulseController.stop();
    _paymentStatusTimer?.cancel();

    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        context.go('/order-confirmation', extra: widget.order);
      }
    });
  }

  void _handlePaymentError(String error) {
    setState(() {
      isProcessing = false;
      statusMessage = 'Payment failed: $error';
    });

    _pulseController.stop();
    _paymentStatusTimer?.cancel();

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        context.go('/payment-failed', extra: error);
      }
    });
  }

  void _cancelPayment() {
    _paymentStatusTimer?.cancel();
    _pulseController.stop();
    if (mounted) {
      context.pop('failed');
    }
  }

  @override
  void dispose() {
    _paymentStatusTimer?.cancel();
    _pulseController.dispose();
    _cardController.dispose();
    loadingDialog?.dismiss();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('EMV Payment', style: TextStyle(fontSize: 24)),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        leading: IconButton(icon: const Icon(Icons.close), onPressed: _cancelPayment),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Payment amount
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 10, offset: const Offset(0, 4)),
                  ],
                ),
                child: Column(
                  children: [
                    const Text(
                      'Total Amount',
                      style: TextStyle(fontSize: 16, color: Colors.grey, fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '\$${widget.order.grandTotal.toStringAsFixed(2)}',
                      style: const TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: AppColors.primary),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // EMV device info
              if (selectedEmv != null) ...[
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(Icons.point_of_sale, color: AppColors.primary, size: 24),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(selectedEmv!.label, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                            const SizedBox(height: 4),
                            Text(selectedEmv!.deviceType, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.green.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(color: Colors.green),
                        ),
                        child: Text(
                          selectedEmv!.status,
                          style: const TextStyle(color: Colors.green, fontSize: 12, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
              ],

              // Card animation and instructions
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AnimatedBuilder(
                      animation: _pulseAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _pulseAnimation.value,
                          child: AnimatedBuilder(
                            animation: _cardAnimation,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _cardAnimation.value,
                                child: Container(
                                  width: 120,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.primary.withValues(alpha: 0.3),
                                        blurRadius: 20,
                                        offset: const Offset(0, 8),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(Icons.credit_card, color: Colors.white, size: 40),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 32),

                    Text(
                      statusMessage,
                      style: const TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: Colors.black87),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    if (!isProcessing) ...[
                      const Text(
                        'Insert, tap, or swipe your card on the payment terminal',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                        textAlign: TextAlign.center,
                      ),
                    ] else ...[
                      const CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary)),
                      const SizedBox(height: 16),
                      const Text('Processing payment...', style: TextStyle(fontSize: 16, color: Colors.grey)),
                    ],
                  ],
                ),
              ),

              // Cancel button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: isProcessing ? null : _cancelPayment,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: const BorderSide(color: Colors.grey),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: const Text('Cancel Payment', style: TextStyle(fontSize: 16, color: Colors.grey)),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
