import 'package:cached_network_image/cached_network_image.dart';
import 'package:dineazy_guest_kiosk/widgets/login_card.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../common/constants/colors.dart';
import '../../core/utils.dart';
import '../../models/order/order_type.dart';
import '../../providers/auth_provider.dart';
import '../../providers/cart_provider.dart';
import '../../providers/data_provider.dart';
import '../../widgets/loading_dialog.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _floatingAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  bool _isInitializing = false;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _floatingController = AnimationController(duration: const Duration(seconds: 4), vsync: this)..repeat(reverse: true);

    _pulseController = AnimationController(duration: const Duration(milliseconds: 1500), vsync: this)
      ..repeat(reverse: true);

    _fadeController = AnimationController(duration: const Duration(milliseconds: 800), vsync: this);

    _slideController = AnimationController(duration: const Duration(milliseconds: 1000), vsync: this);

    // Initialize animations
    _floatingAnimation = Tween<double>(
      begin: -8,
      end: 8,
    ).animate(CurvedAnimation(parent: _floatingController, curve: Curves.easeInOut));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.08,
    ).animate(CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut));

    _fadeAnimation = CurvedAnimation(parent: _fadeController, curve: Curves.easeOut);

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.elasticOut));

    // Start entrance animations
    _fadeController.forward();
    _slideController.forward();

    // Initialize session if coming from persistent login
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSessionIfNeeded();
    });
  }

  Future<void> _initializeSessionIfNeeded() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final dataProvider = Provider.of<DataProvider>(context, listen: false);

    // If user has valid session but restaurant not loaded, initialize
    if (authProvider.hasValidSession() && !dataProvider.hasRestaurant && !_isInitializing) {
      setState(() {
        _isInitializing = true;
      });

      await authProvider.initializeSession(context);

      setState(() {
        _isInitializing = false;
      });
    }
  }

  Future<void> _loadMenuAndNavigate(BuildContext context) async {
    final dataProvider = Provider.of<DataProvider>(context, listen: false);
    final CartProvider cartProvider = Provider.of<CartProvider>(context, listen: false);

    if (!dataProvider.hasRestaurant) {
      debugPrint('HomeScreen._loadMenuAndNavigate: Restaurant not loaded');
      return;
    }

    // Show loading dialog
    LoadingDialog? loadingDialog;
    if (mounted) {
      loadingDialog = LoadingDialog(context, 'Loading menu...');
      loadingDialog.show();
    }

    try {
      await dataProvider.loadMenu(dataProvider.restaurant, OrderType.takeaway);

      if (!dataProvider.isMenuLoading) {
        loadingDialog?.dismiss();
        if (context.mounted) {
          cartProvider.clear();
          context.push('/menu');
        }
      }
    } catch (e) {
      loadingDialog?.dismiss();
      debugPrint('HomeScreen._loadMenuAndNavigate: Error loading menu: $e');

      // Show error message
      if (context.mounted) {
        showErrorSnackBar(context, 'Failed to load menu: $e');
      }
    }
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _pulseController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final DataProvider dataProvider = Provider.of<DataProvider>(context);
    if (!dataProvider.hasRestaurant || _isInitializing) {
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(gradient: AppColors.primaryGradient),
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
              strokeWidth: 3,
            ),
          ),
        ),
      );
    }

    final restaurantDetails = dataProvider.restaurantInfo;

    return Scaffold(
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Stack(
          children: [
            // Main Content
            SafeArea(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 20),
                    child: Column(
                      children: [
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              restaurantDetails.logoUrl != null
                                  ? Container(
                                      width: 140,
                                      height: 140,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: LinearGradient(
                                          colors: [
                                            AppColors.primary.withValues(alpha: 0.15),
                                            AppColors.primary.withValues(alpha: 0.08),
                                          ],
                                        ),
                                        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3), width: 3),
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.primary.withValues(alpha: 0.2),
                                            offset: const Offset(0, 12),
                                            blurRadius: 32,
                                            spreadRadius: 0,
                                          ),
                                        ],
                                      ),
                                      child: ClipOval(
                                        child: CachedNetworkImage(
                                          fit: BoxFit.cover,
                                          errorWidget: (context, url, error) => _buildAnimatedHeaderIcon(),
                                          imageUrl: restaurantDetails.logoUrl!,
                                        ),
                                      ),
                                    )
                                  : _buildAnimatedHeaderIcon(),

                              const SizedBox(height: 32),

                              // Welcome Text
                              Text(
                                'Welcome to',
                                style: TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),

                              const SizedBox(height: 8),

                              ShaderMask(
                                shaderCallback: (bounds) => AppColors.primaryGradient.createShader(bounds),
                                child: Text(
                                  restaurantDetails.name,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 52,
                                    fontWeight: FontWeight.w900,
                                    letterSpacing: -2,
                                    height: 1.1,
                                  ),
                                ),
                              ),

                              // Enhanced Action Button
                              const SizedBox(height: 96),
                              AnimatedBuilder(
                                animation: _pulseAnimation,
                                builder: (context, child) {
                                  return Transform.scale(
                                    scale: _pulseAnimation.value,
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(28),
                                        gradient: AppColors.buttonGradient,
                                        boxShadow: [
                                          BoxShadow(
                                            color: AppColors.primary.withValues(alpha: 0.4),
                                            offset: const Offset(0, 12),
                                            blurRadius: 32,
                                            spreadRadius: 0,
                                          ),
                                        ],
                                      ),
                                      child: Material(
                                        color: Colors.transparent,
                                        child: InkWell(
                                          borderRadius: BorderRadius.circular(28),
                                          onTap: () async {
                                            await _loadMenuAndNavigate(context);
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 56, vertical: 24),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(Icons.restaurant_menu_rounded, size: 28, color: AppColors.white),
                                                const SizedBox(width: 16),
                                                const Text(
                                                  'Browse Menu',
                                                  style: TextStyle(
                                                    fontSize: 20,
                                                    fontWeight: FontWeight.w700,
                                                    color: AppColors.white,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),

                        // Enhanced Features Section
                        Container(
                          padding: const EdgeInsets.all(24),
                          margin: const EdgeInsets.only(top: 20, bottom: 20),
                          child: Row(
                            children: [
                              _buildEnhancedFeature(
                                Icons.flash_on_rounded,
                                'Fast Service',
                                'Order in under 2 minutes',
                                AppColors.warning,
                              ),
                              _buildDivider(),
                              _buildEnhancedFeature(
                                Icons.contactless_rounded,
                                'Easy Payment',
                                'Card or mobile pay',
                                AppColors.primary,
                              ),
                              _buildDivider(),
                              _buildEnhancedFeature(
                                Icons.schedule_rounded,
                                'No Waiting',
                                'Skip the line entirely',
                                AppColors.success,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Enhanced Settings Menu
            Positioned(
              top: MediaQuery.of(context).padding.top + 8,
              right: 16,
              child: IconButton(
                icon: Icon(Icons.more_vert_rounded, color: Colors.grey.shade200, size: 20),
                onPressed: () async {
                  final success = await _showLoginDialog(context);
                  if (success && context.mounted) {
                    _showSettingsMenu(context);
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<bool> _showLoginDialog(BuildContext context) async {
    if(kDebugMode) {
      return true;
    }
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: Colors.white,
          child: Stack(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: LoginCard(
                  title: 'Account Verification',
                  subtitle: 'Verify your account to access the kiosk system.',
                  hasBoxShadow: false,
                  isLoading: false,
                  onPressed: () async {
                    final authProvider = Provider.of<AuthProvider>(context, listen: false);
                    await authProvider.checkLogin(context);
                    if (context.mounted) {
                      Navigator.of(context).pop(true);
                    }
                  },
                ),
              ),

              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.close, size: 22, color: Colors.black54),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
    return result ?? false;
  }

  void _showSettingsMenu(BuildContext context) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    final selected = await showMenu<String>(
      context: context,
      position: RelativeRect.fromLTRB(
        MediaQuery.of(context).size.width - 60,
        MediaQuery.of(context).padding.top + 56,
        16,
        0,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      items: [
        PopupMenuItem(
          value: 'logout',
          child: Row(
            children: [
              Icon(Icons.logout_rounded, color: Colors.red, size: 16),
              const SizedBox(width: 12),
              const Text('Logout'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'setup_printer',
          child: Row(
            children: [
              Icon(Icons.print_rounded, color: Colors.blue, size: 16),
              const SizedBox(width: 12),
              const Text('Setup Printer'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'setup_emv',
          child: Row(
            children: [
              Icon(Icons.credit_card_rounded, color: Colors.green, size: 16),
              const SizedBox(width: 12),
              const Text('Setup EMV'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'setup_menu',
          child: Row(
            children: [
              Icon(Icons.restaurant_menu_rounded, color: Colors.orange, size: 16),
              const SizedBox(width: 12),
              const Text('Setup Menu'),
            ],
          ),
        ),
      ],
    );

    switch (selected) {
      case 'logout':
        authProvider.logout();
        if (context.mounted) context.go("/");
        break;
      case 'setup_printer':
        if (context.mounted) context.push("/printer/setup");
        break;
      case 'setup_emv':
        if (context.mounted) context.push("/emv/setup");
        break;
      case 'setup_menu':
        if (context.mounted) context.push("/setup-menu");
        break;
    }
  }

  Widget _buildAnimatedHeaderIcon() {
    return AnimatedBuilder(
      animation: _floatingAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _floatingAnimation.value),
          child: Container(
            width: 140,
            height: 140,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [AppColors.primary.withValues(alpha: 0.15), AppColors.primary.withValues(alpha: 0.08)],
              ),
              border: Border.all(color: AppColors.primary.withValues(alpha: 0.3), width: 3),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primary.withValues(alpha: 0.2),
                  offset: const Offset(0, 12),
                  blurRadius: 32,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Icon(Icons.touch_app_rounded, size: 56, color: AppColors.primary),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedFeature(IconData icon, String title, String description, Color accentColor) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [accentColor.withValues(alpha: 0.1), accentColor.withValues(alpha: 0.05)],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: accentColor.withValues(alpha: 0.2), width: 1),
            ),
            child: Icon(icon, color: accentColor, size: 32),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(color: AppColors.textPrimary, fontSize: 16, fontWeight: FontWeight.w700),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 6),
          Text(
            description,
            style: TextStyle(color: AppColors.textSecondary, fontSize: 13, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      width: 1,
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.transparent, AppColors.cardBorder, Colors.transparent],
        ),
      ),
    );
  }
}
