import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_thermal_printer/flutter_thermal_printer.dart';
import 'package:flutter_thermal_printer/utils/printer.dart';
import 'package:provider/provider.dart';

import '../../../core/utils.dart';
import '../../../providers/data_provider.dart';

class SetupPrinterViewModel extends ChangeNotifier {
  final FlutterThermalPrinter _plugin = FlutterThermalPrinter.instance;

  final List<Printer> _printers = [];

  List<Printer> get printers => List.unmodifiable(_printers);

  StreamSubscription<List<Printer>>? _devicesStreamSubscription;

  bool _isScanning = false;

  bool get isScanning => _isScanning;

  Future<void> startScan() async {
    _isScanning = true;
    notifyListeners();
    await _devicesStreamSubscription?.cancel();

    await _plugin.getPrinters(connectionTypes: [ConnectionType.USB, ConnectionType.BLE, ConnectionType.NETWORK]);

    _devicesStreamSubscription = _plugin.devicesStream.listen((List<Printer> event) {
      _printers
        ..clear()
        ..addAll(event.where((p) => (p.name ?? '').isNotEmpty));
      notifyListeners();
    });
  }

  void stopScan() {
    _isScanning = false;
    _plugin.stopScan();
    _devicesStreamSubscription?.cancel();
    notifyListeners();
  }

  Future<void> testPrint(BuildContext context, Printer printer) async {
    try {
      if (!(printer.isConnected ?? false)) {
        await _plugin.connect(printer);
      }
      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm80, profile);
      List<int> bytes = [];
      bytes += generator.text(
        'Dineazy Kiosk',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size3,
          width: PosTextSize.size3,
        ),
      );
      bytes += generator.text(
        'Test Print',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size2,
          width: PosTextSize.size2,
        ),
      );
      bytes += generator.text('Printer: ${printer.name}');
      bytes += generator.text('Type: ${printer.connectionTypeString}');
      bytes += generator.feed(2);
      bytes += generator.cut();

      await _plugin.printData(printer, bytes, longData: true);
      await _plugin.disconnect(printer);
      if (context.mounted) {
        showSnackBar(context, 'Test print successful for ${printer.name}');
      }
    } catch (e) {
      if (context.mounted) {
        showErrorSnackBar(context, 'Error printing: $e');
      }
    }
  }

  Map<String, List<Printer>> groupPrintersByType() {
    final Map<String, List<Printer>> groupedPrinters = {'USB': [], 'Bluetooth': [], 'Network': [], 'Other': []};

    for (var printer in _printers) {
      switch (printer.connectionType) {
        case ConnectionType.USB:
          groupedPrinters['USB']!.add(printer);
          break;
        case ConnectionType.BLE:
          groupedPrinters['Bluetooth']!.add(printer);
          break;
        case ConnectionType.NETWORK:
          groupedPrinters['Network']!.add(printer);
          break;
        default:
          groupedPrinters['Other']!.add(printer);
          break;
      }
    }

    groupedPrinters.removeWhere((key, value) => value.isEmpty);
    return groupedPrinters;
  }

  Future<void> selectPrinter(BuildContext context, Printer printer) async {
    final dataProvider = Provider.of<DataProvider>(context, listen: false);
    await dataProvider.addBillPrinter(context, printer);
  }

  @override
  void dispose() {
    _devicesStreamSubscription?.cancel();
    _plugin.stopScan();
    super.dispose();
  }
}
