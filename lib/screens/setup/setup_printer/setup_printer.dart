import 'package:flutter/material.dart';
import 'package:flutter_thermal_printer/utils/printer.dart';
import 'package:provider/provider.dart';

import '../../../common/constants/colors.dart';
import 'setup_printer.vm.dart';

class SetupPrinter extends StatelessWidget {
  const SetupPrinter({super.key});

  IconData _getConnectionTypeIcon(String connectionType) {
    switch (connectionType) {
      case 'USB':
        return Icons.usb;
      case 'Bluetooth':
        return Icons.bluetooth;
      case 'Network':
        return Icons.wifi;
      default:
        return Icons.print;
    }
  }

  Color _getConnectionTypeColor(String connectionType) {
    switch (connectionType) {
      case 'USB':
        return Colors.blue;
      case 'Bluetooth':
        return Colors.indigo;
      case 'Network':
        return Colors.green;
      default:
        return AppColors.primary;
    }
  }

  Widget _buildPrinterCard(Printer printer, VoidCallback onTestPrint, VoidCallback onSelectPrinter) {
    final isConnected = printer.isConnected ?? false;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.primary.withValues(alpha: 0.8), AppColors.primary.withValues(alpha: 0.6)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: AppColors.primary.withValues(alpha: 0.3), blurRadius: 8, offset: const Offset(0, 4)),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Status indicator
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: isConnected ? Colors.green : Colors.orange,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: (isConnected ? Colors.green : Colors.orange).withValues(alpha: 0.5),
                    blurRadius: 4,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            // Printer info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    printer.name ?? 'Unknown Printer',
                    style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(_getConnectionTypeIcon(printer.connectionTypeString), color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        printer.connectionTypeString,
                        style: TextStyle(color: Colors.white.withValues(alpha: 0.8), fontSize: 14),
                      ),
                      const SizedBox(width: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: (isConnected ? Colors.green : Colors.orange).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: isConnected ? Colors.green : Colors.orange, width: 1),
                        ),
                        child: Text(
                          isConnected ? 'Connected' : 'Disconnected',
                          style: TextStyle(
                            color: isConnected ? Colors.green : Colors.orange,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // Test print button
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: onTestPrint,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.print, color: Colors.white, size: 16),
                        SizedBox(width: 6),
                        Text(
                          'Test Print',
                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // Select printer button
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(8),
                  onTap: onSelectPrinter,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.check, color: Colors.white, size: 16),
                        SizedBox(width: 6),
                        Text(
                          'Select Printer',
                          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGroupHeader(String connectionType, int count) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getConnectionTypeColor(connectionType).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getConnectionTypeColor(connectionType).withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(_getConnectionTypeIcon(connectionType), color: _getConnectionTypeColor(connectionType), size: 24),
          const SizedBox(width: 12),
          Text(
            '$connectionType Printers',
            style: TextStyle(color: _getConnectionTypeColor(connectionType), fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getConnectionTypeColor(connectionType),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              count.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) {
        final vm = SetupPrinterViewModel();
        vm.startScan();
        return vm;
      },
      child: Consumer<SetupPrinterViewModel>(
        builder: (context, vm, _) {
          final groupedPrinters = vm.groupPrintersByType();

          return Scaffold(
            backgroundColor: Colors.grey[100],
            appBar: AppBar(
              title: const Text('Printer Setup', style: TextStyle(fontSize: 24)),
              elevation: 0,
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.white,
            ),
            body: Column(
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: vm.isScanning ? null : vm.startScan,
                          icon: vm.isScanning
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(Icons.search, size: 18),
                          label: Text(vm.isScanning ? 'Scanning...' : 'Scan', style: TextStyle(fontSize: 14)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: vm.isScanning ? Colors.grey : AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            elevation: 2,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton.icon(
                        onPressed: vm.stopScan,
                        icon: const Icon(Icons.stop, size: 18),
                        label: const Text('Stop', style: TextStyle(fontSize: 14)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                          elevation: 2,
                        ),
                      ),
                    ],
                  ),
                ),

                // Printer list
                Expanded(
                  child: vm.printers.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(32),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.2),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  vm.isScanning ? Icons.search : Icons.print_disabled,
                                  size: 64,
                                  color: vm.isScanning ? AppColors.primary : Colors.grey,
                                ),
                              ),
                              const SizedBox(height: 24),
                              Text(
                                vm.isScanning ? 'Scanning for printers...' : 'No printers found',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: Colors.grey[600]),
                              ),
                              if (!vm.isScanning) ...[
                                const SizedBox(height: 8),
                                Text(
                                  'Tap "Scan Printers" to search for available devices',
                                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.only(bottom: 16),
                          itemCount: groupedPrinters.entries.length * 2,
                          itemBuilder: (context, index) {
                            if (index.isEven) {
                              // Group header
                              final groupIndex = index ~/ 2;
                              final entry = groupedPrinters.entries.elementAt(groupIndex);
                              return _buildGroupHeader(entry.key, entry.value.length);
                            } else {
                              // Printers in group
                              final groupIndex = index ~/ 2;
                              final entry = groupedPrinters.entries.elementAt(groupIndex);
                              return Column(
                                children: entry.value
                                    .map(
                                      (printer) => Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 4),
                                        child: _buildPrinterCard(
                                          printer,
                                          () => vm.testPrint(context, printer),
                                          () => vm.selectPrinter(context, printer),
                                        ),
                                      ),
                                    )
                                    .toList(),
                              );
                            }
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
