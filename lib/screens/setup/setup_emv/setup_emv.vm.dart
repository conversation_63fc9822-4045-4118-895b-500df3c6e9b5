import 'dart:convert';

import 'package:flutter/material.dart';

import '../../../api/api_manager.dart';
import '../../../common/globals.dart';
import '../../../models/emv.dart';

class SetupEmvViewModel extends ChangeNotifier {
  final EmvList _emvs = [];
  Emv? _selectedEmv;
  bool _isLoading = false;

  SetupEmvViewModel() {
    final selectedEmvJson = sharedPreferences.getString("selected_emv");
    if (selectedEmvJson != null) {
      try {
        final selectedEmv = Emv(jsonDecode(selectedEmvJson));
        _selectedEmv = selectedEmv;
      } catch (e) {
        debugPrint("SetupEmvViewModel constructor ❌ ERROR: $e");
      }
    }
  }

  EmvList get emvs => _emvs;

  Emv? get selectedEmv => _selectedEmv;

  bool get isLoading => _isLoading;

  set selectedEmv(Emv? emv) {
    _selectedEmv = emv;
    sharedPreferences.setString("selected_emv", jsonEncode(emv?.json));
    notifyListeners();
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void setEmvs(EmvList emvs) {
    _emvs
      ..clear()
      ..addAll(emvs);
    notifyListeners();
  }

  Future<void> fetchEmvs(String companyId, String revenueCenterId) async {
    try {
      _setLoading(true);
      final apiManager = ApiManager.tokenInstance();
      final emvList = await apiManager.getEmvs(companyId, revenueCenterId);
      setEmvs(emvList.toList());
    } catch (e) {
      debugPrint("SetupEmvViewModel.fetchEmvs ❌ ERROR: $e");
    } finally {
      _setLoading(false);
    }
  }
}
