import 'package:dineazy_guest_kiosk/core/utils.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../../common/constants/colors.dart';
import '../../../models/emv.dart';
import '../../../providers/data_provider.dart';
import 'setup_emv.vm.dart';

class SetupEmvScreen extends StatelessWidget {
  const SetupEmvScreen({super.key});

  IconData _getEmvTypeIcon(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return Icons.phone_android;
      case 'terminal':
        return Icons.point_of_sale;
      case 'countertop':
        return Icons.desktop_mac;
      case 'pinpad':
        return Icons.dialpad;
      default:
        return Icons.credit_card;
    }
  }

  Color _getEmvTypeColor(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return Colors.blue;
      case 'terminal':
        return Colors.green;
      case 'countertop':
        return Colors.purple;
      case 'pinpad':
        return Colors.orange;
      default:
        return AppColors.primary;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'online':
      case 'connected':
      case 'active':
        return Colors.green;
      case 'offline':
      case 'disconnected':
        return Colors.yellow;
      case 'idle':
      case 'standby':
        return Colors.yellowAccent;
      default:
        return Colors.grey;
    }
  }

  Widget _buildEmvCard(Emv emv, bool isSelected, VoidCallback onSelect) {
    final statusColor = _getStatusColor(emv.status);
    final isOnline =
        emv.status.toLowerCase() == 'online' ||
        emv.status.toLowerCase() == 'connected' ||
        emv.status.toLowerCase() == 'active';

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isSelected
              ? [AppColors.primary.withValues(alpha: 0.9), AppColors.primary.withValues(alpha: 0.7)]
              : [AppColors.primary.withValues(alpha: 0.8), AppColors.primary.withValues(alpha: 0.6)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isSelected ? AppColors.primary.withValues(alpha: 0.4) : AppColors.primary.withValues(alpha: 0.3),
            blurRadius: isSelected ? 12 : 8,
            offset: const Offset(0, 4),
          ),
        ],
        border: isSelected ? Border.all(color: Colors.white.withValues(alpha: 0.5), width: 2) : null,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Status indicator
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
                boxShadow: [BoxShadow(color: statusColor.withValues(alpha: 0.5), blurRadius: 4, spreadRadius: 1)],
              ),
            ),
            const SizedBox(width: 16),
            // EMV info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    emv.label,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: isSelected ? FontWeight.w800 : FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(_getEmvTypeIcon(emv.deviceType), color: Colors.white70, size: 16),
                      const SizedBox(width: 4),
                      Text(emv.deviceType, style: TextStyle(color: Colors.white.withValues(alpha: 0.8), fontSize: 14)),
                      const SizedBox(width: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: statusColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: statusColor, width: 1),
                        ),
                        child: Text(
                          emv.status,
                          style: TextStyle(color: statusColor, fontSize: 12, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Serial: ${emv.serialNumber}',
                    style: TextStyle(color: Colors.white.withValues(alpha: 0.7), fontSize: 12),
                  ),
                ],
              ),
            ),
            if (isOnline) ...[
              Container(
                decoration: BoxDecoration(
                  color: isSelected ? Colors.white.withValues(alpha: 0.25) : Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(8),
                    onTap: onSelect,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(isSelected ? Icons.check_circle : Icons.check, color: Colors.white, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            isSelected ? 'Selected' : 'Select',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildGroupHeader(String deviceType, int count) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getEmvTypeColor(deviceType).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getEmvTypeColor(deviceType).withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        children: [
          Icon(_getEmvTypeIcon(deviceType), color: _getEmvTypeColor(deviceType), size: 24),
          const SizedBox(width: 12),
          Text(
            '$deviceType Devices',
            style: TextStyle(color: _getEmvTypeColor(deviceType), fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(color: _getEmvTypeColor(deviceType), borderRadius: BorderRadius.circular(12)),
            child: Text(
              count.toString(),
              style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) {
        final DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
        final setupEmvViwModel = SetupEmvViewModel();
        setupEmvViwModel.fetchEmvs(dataProvider.companyId!, dataProvider.revenueCenterId!);
        return setupEmvViwModel;
      },
      child: Consumer<SetupEmvViewModel>(
        builder: (context, setupEmvViwModel, _) {
          final DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
          final groupedEmvs = <String, List<Emv>>{};
          for (final emv in setupEmvViwModel.emvs) {
            groupedEmvs.putIfAbsent(emv.deviceType, () => []).add(emv);
          }

          return Scaffold(
            backgroundColor: Colors.grey[100],
            appBar: AppBar(
              title: const Text('EMV Setup', style: TextStyle(fontSize: 24)),
              elevation: 0,
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            body: Column(
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: setupEmvViwModel.isLoading
                              ? null
                              : () =>
                                    setupEmvViwModel.fetchEmvs(dataProvider.companyId!, dataProvider.revenueCenterId!),
                          icon: setupEmvViwModel.isLoading
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Icon(Icons.refresh, size: 18),
                          label: Text(
                            setupEmvViwModel.isLoading ? 'Loading...' : 'Refresh',
                            style: const TextStyle(fontSize: 14),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: setupEmvViwModel.isLoading ? Colors.grey : AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            elevation: 2,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // EMV list
                Expanded(
                  child: setupEmvViwModel.isLoading
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(32),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.2),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: const CircularProgressIndicator(
                                  strokeWidth: 3,
                                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                                ),
                              ),
                              const SizedBox(height: 24),
                              Text(
                                'Loading EMV devices...',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: Colors.grey[600]),
                              ),
                            ],
                          ),
                        )
                      : setupEmvViwModel.emvs.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(32),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.2),
                                      blurRadius: 20,
                                      offset: const Offset(0, 10),
                                    ),
                                  ],
                                ),
                                child: Icon(Icons.credit_card_off, size: 64, color: Colors.grey[400]),
                              ),
                              const SizedBox(height: 24),
                              Text(
                                'No EMV devices found',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: Colors.grey[600]),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Contact your administrator to set up EMV devices',
                                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.only(bottom: 16),
                          itemCount: groupedEmvs.entries.length * 2,
                          itemBuilder: (context, index) {
                            if (index.isEven) {
                              // Group header
                              final groupIndex = index ~/ 2;
                              final entry = groupedEmvs.entries.elementAt(groupIndex);
                              return _buildGroupHeader(entry.key, entry.value.length);
                            } else {
                              // EMVs in group
                              final groupIndex = index ~/ 2;
                              final entry = groupedEmvs.entries.elementAt(groupIndex);
                              return Column(
                                children: entry.value
                                    .map(
                                      (emv) => Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 4),
                                        child: _buildEmvCard(emv, setupEmvViwModel.selectedEmv?.id == emv.id, () {
                                          setupEmvViwModel.selectedEmv = emv;
                                          showSnackBar(context, 'EMV ${emv.label} selected');
                                          context.pop();
                                        }),
                                      ),
                                    )
                                    .toList(),
                              );
                            }
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
