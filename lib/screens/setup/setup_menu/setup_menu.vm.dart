import 'package:dineazy_guest_kiosk/widgets/loading_dialog.dart';
import 'package:flutter/cupertino.dart';

import '../../../api/api_manager.dart';
import '../../../models/food/food_category.dart';
import '../../../models/food/food_item.dart';
import '../../../models/food/food_sub_category.dart';
import '../../../models/order/order_type.dart';
import '../../../models/restaurant/restaurant.dart';
import '../../../models/restaurant/restaurant_menu.dart';

class SetupMenuViewModel extends ChangeNotifier {
  String? _loadingText;

  String? get loadingText => _loadingText;

  RestaurantMenu? _menu;

  RestaurantMenu? get menu => _menu;

  // Lookup maps for quick access
  final Map<String, FoodCategory> _categoryById = {};
  final Map<String, FoodSubCategory> _subCategoryById = {};

  // Saved overrides after a successful save
  final Map<String, _ItemOverrides> _savedOverrides = {};

  // Pending edits since last save
  final Map<String, _ItemOverrides> _pendingEdits = {};

  // Search query
  String _query = '';

  // Saving state
  bool _saving = false;

  bool get isSaving => _saving;

  bool get hasChanges => _pendingEdits.isNotEmpty;

  set query(String value) {
    _query = value.trim().toLowerCase();
    notifyListeners();
  }

  String get queryText => _query;

  Iterable<FoodItem> get allItems => _menu?.allFoodItems ?? [];

  Iterable<FoodItem> get filteredItems {
    if (_menu == null) return const [];
    if (_query.isEmpty) return allItems;
    return allItems.where((item) {
      final name = item.name.toLowerCase();
      final categoryName = _categoryById[item.categoryId]?.name.toLowerCase() ?? '';
      final subCategoryName = _subCategoryById[item.subCategoryId]?.name.toLowerCase() ?? '';
      return name.contains(_query) || categoryName.contains(_query) || subCategoryName.contains(_query);
    });
  }

  String categoryNameOf(FoodItem item) => _categoryById[item.categoryId]?.name ?? '-';

  String subCategoryNameOf(FoodItem item) => _subCategoryById[item.subCategoryId]?.name ?? '-';

  double currentPriceOf(FoodItem item) {
    final id = item.id;
    if (_pendingEdits[id]?.price != null) return _pendingEdits[id]!.price!;
    if (_savedOverrides[id]?.price != null) return _savedOverrides[id]!.price!;
    return item.price;
  }

  String currentPriceStringOf(FoodItem item) {
    final price = currentPriceOf(item);
    final absolute = price.toInt();
    return price == absolute ? '$absolute' : '$price';
  }

  bool isEnabled(FoodItem item) {
    final id = item.id;
    if (_pendingEdits[id]?.enabled != null) return _pendingEdits[id]!.enabled!;
    if (_savedOverrides[id]?.enabled != null) return _savedOverrides[id]!.enabled!;
    return true;
  }

  void setPrice(String itemId, double? price) {
    final existing = _pendingEdits[itemId] ?? _ItemOverrides();
    existing.price = price;
    _pendingEdits[itemId] = existing;
    notifyListeners();
  }

  void setEnabled(String itemId, bool enabled) {
    final existing = _pendingEdits[itemId] ?? _ItemOverrides();
    existing.enabled = enabled;
    _pendingEdits[itemId] = existing;
    notifyListeners();
  }

  Future<void> saveChanges() async {
    if (_pendingEdits.isEmpty) return;
    _saving = true;
    notifyListeners();

    await Future.delayed(const Duration(milliseconds: 800));

    _pendingEdits.forEach((key, value) {
      final saved = _savedOverrides[key] ?? _ItemOverrides();
      if (value.price != null) saved.price = value.price;
      if (value.enabled != null) saved.enabled = value.enabled;
      _savedOverrides[key] = saved;
    });
    _pendingEdits.clear();
    _saving = false;
    notifyListeners();
  }

  void discardChanges() {
    if (_pendingEdits.isEmpty) return;
    _pendingEdits.clear();
    notifyListeners();
  }

  Future<void> loadMenu(BuildContext context,Restaurant restaurant, OrderType orderType) async {
    LoadingDialog loadingDialog = LoadingDialog(context, 'Loading menu...');
    loadingDialog.show();
    _loadingText = "Loading menu...";
    notifyListeners();

    debugPrint("SetupMenuViewModel.loadMenu 🐞, restaurant ${restaurant.name}");

    try {
      ApiManager apiManager = ApiManager.tokenInstance();
      _menu = await apiManager.getAllInventory(
        companyId: restaurant.companyId,
        revenueCenterId: restaurant.revenueCenterId,
        orderTypeId: orderType.id,
      );
      _categoryById
        ..clear()
        ..addEntries(_menu!.categories.map((c) => MapEntry(c.id, c)));
      _subCategoryById
        ..clear()
        ..addEntries(_menu!.subCategories.map((sc) => MapEntry(sc.id, sc)));
      _pendingEdits.clear();
      _savedOverrides.clear();
      _query = '';
    } catch (e) {
      debugPrint("SetupMenuViewModel.loadMenu: ❌ERROR: $e");
    }
    loadingDialog.dismiss();
    _loadingText = null;
    notifyListeners();
  }
}

class _ItemOverrides {
  double? price;
  bool? enabled;
}
