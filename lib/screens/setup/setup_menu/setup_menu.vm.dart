import 'package:flutter/material.dart';

import '../../../api/api_manager.dart';
import '../../../models/food/food_category.dart';
import '../../../models/food/food_item.dart';
import '../../../models/food/food_sub_category.dart';
import '../../../models/order/order_type.dart';
import '../../../models/restaurant/restaurant.dart';
import '../../../models/restaurant/restaurant_menu.dart';

class SetupMenuViewModel extends ChangeNotifier {
  // Loading state
  bool _isLoading = false;
  String? _errorMessage;

  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Menu data
  RestaurantMenu? _menu;
  RestaurantMenu? get menu => _menu;

  // Lookup maps for quick access
  final Map<String, FoodCategory> _categoryById = {};
  final Map<String, FoodSubCategory> _subCategoryById = {};

  // Item overrides - tracks all changes
  final Map<String, _ItemOverrides> _itemOverrides = {};

  // Track which items have pending changes since last save
  final Set<String> _pendingChanges = {};

  // Search functionality
  String _searchQuery = '';
  String get searchQuery => _searchQuery;

  // Saving state
  bool _isSaving = false;
  bool get isSaving => _isSaving;

  // Computed properties
  bool get hasChanges => _pendingChanges.isNotEmpty;
  int get changesCount => _pendingChanges.length;
  bool get hasData => _menu != null && _menu!.allFoodItems.isNotEmpty;

  Iterable<FoodItem> get allItems => _menu?.allFoodItems ?? [];

  Iterable<FoodItem> get filteredItems {
    if (_menu == null) return const [];

    var items = allItems;

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      items = items.where((item) {
        final name = item.name.toLowerCase();
        final categoryName = _categoryById[item.categoryId]?.name.toLowerCase() ?? '';
        final subCategoryName = _subCategoryById[item.subCategoryId]?.name.toLowerCase() ?? '';
        return name.contains(query) ||
               categoryName.contains(query) ||
               subCategoryName.contains(query);
      });
    }

    return items;
  }

  // Search functionality
  void updateSearchQuery(String query) {
    _searchQuery = query.trim();
    notifyListeners();
  }

  void clearSearch() {
    _searchQuery = '';
    notifyListeners();
  }

  // Helper methods for item data
  String categoryNameOf(FoodItem item) {
    return _categoryById[item.categoryId]?.name ?? 'Unknown';
  }

  String subCategoryNameOf(FoodItem item) {
    return _subCategoryById[item.subCategoryId]?.name ?? 'Unknown';
  }

  double getCurrentPrice(FoodItem item) {
    final overrides = _itemOverrides[item.id];
    return overrides?.price ?? item.price;
  }

  String getCurrentPriceString(FoodItem item) {
    final price = getCurrentPrice(item);
    return price.toStringAsFixed(2);
  }

  bool isItemEnabled(FoodItem item) {
    final overrides = _itemOverrides[item.id];
    return overrides?.enabled ?? true; // Default to enabled
  }

  bool hasItemChanged(String itemId) {
    return _pendingChanges.contains(itemId);
  }

  // Item modification methods
  void updateItemPrice(String itemId, double? price) {
    if (price == null || price < 0) return;

    final overrides = _itemOverrides[itemId] ?? _ItemOverrides();
    overrides.price = price;
    _itemOverrides[itemId] = overrides;
    _pendingChanges.add(itemId);
    notifyListeners();
  }

  void updateItemStatus(String itemId, bool enabled) {
    final overrides = _itemOverrides[itemId] ?? _ItemOverrides();
    overrides.enabled = enabled;
    _itemOverrides[itemId] = overrides;
    _pendingChanges.add(itemId);
    notifyListeners();
  }

  // Save and discard operations
  Future<void> saveChanges() async {
    if (_pendingChanges.isEmpty) return;

    _isSaving = true;
    notifyListeners();

    try {
      // Simulate API call - replace with actual API call later
      await Future.delayed(const Duration(milliseconds: 1500));

      // TODO: Implement actual API calls to update items
      // For now, just clear pending changes to simulate successful save
      _pendingChanges.clear();

    } catch (e) {
      _errorMessage = 'Failed to save changes: $e';
      debugPrint('❌ Save failed: $e');
    } finally {
      _isSaving = false;
      notifyListeners();
    }
  }

  void discardChanges() {
    if (_pendingChanges.isEmpty) return;

    // Remove all pending changes
    for (final itemId in _pendingChanges) {
      _itemOverrides.remove(itemId);
    }
    _pendingChanges.clear();
    notifyListeners();
  }

  // Menu loading
  Future<void> loadMenu(BuildContext context, Restaurant restaurant, OrderType orderType) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      final apiManager = ApiManager.tokenInstance();
      _menu = await apiManager.getAllInventory(
        companyId: restaurant.companyId,
        revenueCenterId: restaurant.revenueCenterId,
        orderTypeId: orderType.id,
      );

      // Build lookup maps
      _categoryById.clear();
      _subCategoryById.clear();

      for (final category in _menu!.categories) {
        _categoryById[category.id] = category;
      }

      for (final subCategory in _menu!.subCategories) {
        _subCategoryById[subCategory.id] = subCategory;
      }

      // Clear any existing changes
      _itemOverrides.clear();
      _pendingChanges.clear();
      _searchQuery = '';

    } catch (e) {
      _errorMessage = 'Failed to load menu: $e';
      debugPrint('❌ Load menu failed: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Cleanup
  @override
  void dispose() {
    _categoryById.clear();
    _subCategoryById.clear();
    _itemOverrides.clear();
    _pendingChanges.clear();
    super.dispose();
  }
}

class _ItemOverrides {
  double? price;
  bool? enabled;
}
