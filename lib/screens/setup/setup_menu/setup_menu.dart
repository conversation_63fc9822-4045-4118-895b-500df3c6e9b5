import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';

import '../../../models/order/order_type.dart';
import '../../../providers/data_provider.dart';
import 'setup_menu.vm.dart';

class SetupMenu extends StatefulWidget {
  const SetupMenu({super.key});

  @override
  State<SetupMenu> createState() => _SetupMenuState();
}

class _SetupMenuState extends State<SetupMenu> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late ScrollController _verticalScrollController;
  late ScrollController _horizontalScrollController;

  @override
  void initState() {
    super.initState();

    _fadeController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut));

    _verticalScrollController = ScrollController();
    _horizontalScrollController = ScrollController();

    SchedulerBinding.instance.addPostFrameCallback((timeStamp) async {
      final dataProvider = Provider.of<DataProvider>(context, listen: false);
      final setupMenuView = Provider.of<SetupMenuViewModel>(context, listen: false);

      await setupMenuView.loadMenu(context, dataProvider.restaurant, OrderType.takeaway);
      _fadeController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _verticalScrollController.dispose();
    _horizontalScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: colorScheme.primary,
        foregroundColor: colorScheme.onPrimary,
        title: Row(
          children: [
            Icon(Icons.restaurant_menu, size: 28),
            const SizedBox(width: 12),
            const Text("Setup Menu", style: TextStyle(fontSize: 24, fontWeight: FontWeight.w600)),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Consumer<SetupMenuViewModel>(
          builder: (context, setupMenuViewModel, _) {
            debugPrint('🐞 _SetupMenuState.build: setupMenuViewModel.loadingText = ${setupMenuViewModel.loadingText}');
            if (setupMenuViewModel.loadingText != null) {
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 12),
                    Text(setupMenuViewModel.loadingText!, style: theme.textTheme.bodyMedium),
                  ],
                ),
              );
            }

            if (setupMenuViewModel.menu == null) {
              return Center(
                child: Text(
                  'No menu data available',
                  style: theme.textTheme.bodyLarge?.copyWith(color: colorScheme.onSurface.withOpacity(0.7)),
                ),
              );
            }

            // Empty search results
            if (setupMenuViewModel.filteredItems.isEmpty) {
              return Column(
                children: [
                  _buildSearchBar(context, setupMenuViewModel),
                  const Spacer(),
                  Center(
                    child: Text(
                      'No items match your search',
                      style: theme.textTheme.bodyLarge?.copyWith(color: colorScheme.onSurface.withOpacity(0.6)),
                    ),
                  ),
                  const Spacer(),
                ],
              );
            }

            // Actual data table
            return Stack(
              children: [
                Column(
                  children: [
                    _buildSearchBar(context, setupMenuViewModel),
                    Expanded(child: _buildTable(context, setupMenuViewModel)),
                    SizedBox(height: setupMenuViewModel.hasChanges ? 80 : 16),
                  ],
                ),
                if (setupMenuViewModel.hasChanges || setupMenuViewModel.isSaving)
                  _buildSaveBar(context, setupMenuViewModel),
              ],
            );
          },
        ),
      ),
    );
  }

  // ---------------- UI Helpers ---------------- //

  Widget _buildSearchBar(BuildContext context, SetupMenuViewModel vm) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        boxShadow: [BoxShadow(color: colorScheme.shadow.withOpacity(0.05), blurRadius: 4, offset: const Offset(0, 2))],
      ),
      child: TextFormField(
        key: ValueKey('query-${vm.queryText}'),
        initialValue: vm.queryText,
        onChanged: (v) => vm.query = v,
        decoration: InputDecoration(
          filled: true,
          fillColor: colorScheme.surfaceVariant.withOpacity(0.3),
          prefixIcon: Icon(Icons.search, color: colorScheme.primary, size: 24),
          hintText: 'Search by item, category, or subcategory...',
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide.none),
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          suffixIcon: vm.queryText.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: colorScheme.onSurface.withOpacity(0.7)),
                  onPressed: () => vm.query = '',
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildTable(BuildContext context, SetupMenuViewModel vm) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [BoxShadow(color: colorScheme.shadow.withOpacity(0.1), blurRadius: 12, offset: const Offset(0, 4))],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Column(
          children: [
            // Table header
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              color: colorScheme.primaryContainer,
              child: Row(
                children: [
                  Icon(Icons.inventory_2_outlined, color: colorScheme.onPrimaryContainer, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Menu Items (${vm.filteredItems.length})',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            // Scrollable table
            Expanded(
              child: Scrollbar(
                controller: _verticalScrollController,
                thumbVisibility: true,
                child: SingleChildScrollView(
                  controller: _verticalScrollController,
                  scrollDirection: Axis.vertical,
                  child: SingleChildScrollView(
                    controller: _horizontalScrollController,
                    scrollDirection: Axis.horizontal,
                    child: Container(
                      constraints: BoxConstraints(minWidth: MediaQuery.of(context).size.width - 32),
                      child: DataTable(
                        headingRowHeight: 56,
                        dataRowMinHeight: 64,
                        dataRowMaxHeight: 72,
                        columnSpacing: 24,
                        horizontalMargin: 16,
                        headingRowColor: WidgetStateProperty.all(colorScheme.surfaceVariant.withOpacity(0.5)),
                        columns: [
                          DataColumn(label: _buildColumnHeader(context, 'Item Name', Icons.restaurant)),
                          DataColumn(label: _buildColumnHeader(context, 'Category', Icons.category)),
                          DataColumn(label: _buildColumnHeader(context, 'Subcategory', Icons.subdirectory_arrow_right)),
                          DataColumn(label: _buildColumnHeader(context, 'Price', Icons.attach_money)),
                          DataColumn(label: _buildColumnHeader(context, 'Status', Icons.toggle_on)),
                        ],
                        rows: [
                          for (int i = 0; i < vm.filteredItems.length; i++)
                            _buildDataRow(context, vm.filteredItems.toList()[i], vm, i),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveBar(BuildContext context, SetupMenuViewModel vm) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Positioned(
      bottom: 16,
      left: 16,
      right: 16,
      child: AnimatedSlide(
        duration: const Duration(milliseconds: 300),
        offset: vm.hasChanges || vm.isSaving ? Offset.zero : const Offset(0, 2),
        child: Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(color: colorScheme.shadow.withOpacity(0.2), blurRadius: 16, offset: const Offset(0, 8)),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: SafeArea(
              top: false,
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: vm.isSaving ? colorScheme.primaryContainer : colorScheme.secondaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      vm.isSaving ? Icons.sync : Icons.edit_note,
                      color: vm.isSaving ? colorScheme.onPrimaryContainer : colorScheme.onSecondaryContainer,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          vm.isSaving ? 'Saving changes...' : 'You have unsaved changes',
                          style: theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        if (!vm.isSaving)
                          Text(
                            'Don\'t forget to save your progress',
                            style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onSurface.withOpacity(0.7)),
                          ),
                      ],
                    ),
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextButton.icon(
                        onPressed: vm.isSaving ? null : vm.discardChanges,
                        icon: const Icon(Icons.close, size: 18),
                        label: const Text('Cancel'),
                        style: TextButton.styleFrom(foregroundColor: colorScheme.error),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: vm.isSaving ? null : vm.saveChanges,
                        icon: vm.isSaving
                            ? SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation(colorScheme.onPrimary),
                                ),
                              )
                            : const Icon(Icons.save, size: 18),
                        label: const Text('Save'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          foregroundColor: colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildColumnHeader(BuildContext context, String title, IconData icon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 18, color: colorScheme.onSurface.withOpacity(0.7)),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600, color: colorScheme.onSurface),
        ),
      ],
    );
  }

  DataRow _buildDataRow(BuildContext context, dynamic item, SetupMenuViewModel viewModel, int index) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isEven = index % 2 == 0;

    return DataRow(
      color: WidgetStateProperty.all(isEven ? colorScheme.surface : colorScheme.surfaceVariant.withOpacity(0.3)),
      cells: [
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  item.name,
                  style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (item.description != null && item.description.isNotEmpty)
                  Text(
                    item.description,
                    style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onSurface.withOpacity(0.6)),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(color: colorScheme.secondaryContainer, borderRadius: BorderRadius.circular(8)),
            child: Text(
              viewModel.categoryNameOf(item),
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onSecondaryContainer,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(color: colorScheme.tertiaryContainer, borderRadius: BorderRadius.circular(8)),
            child: Text(
              viewModel.subCategoryNameOf(item),
              style: theme.textTheme.bodySmall?.copyWith(
                color: colorScheme.onTertiaryContainer,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            width: 120,
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: TextFormField(
              key: ValueKey('price-${item.id}-${viewModel.currentPriceStringOf(item)}'),
              initialValue: viewModel.currentPriceStringOf(item),
              enabled: !viewModel.isSaving,
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              onChanged: (v) => viewModel.setPrice(item.id, double.tryParse(v)),
              style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              decoration: InputDecoration(
                isDense: true,
                prefixText: '\$ ',
                prefixStyle: TextStyle(color: colorScheme.primary, fontWeight: FontWeight.w600),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
                filled: true,
                fillColor: colorScheme.surface,
              ),
            ),
          ),
        ),
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: viewModel.isEnabled(item) ? colorScheme.primaryContainer : colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Switch(
                    value: viewModel.isEnabled(item),
                    onChanged: viewModel.isSaving ? null : (v) => viewModel.setEnabled(item.id, v),
                    activeColor: colorScheme.primary,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: viewModel.isEnabled(item) ? colorScheme.primaryContainer : colorScheme.errorContainer,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    viewModel.isEnabled(item) ? 'Active' : 'Disabled',
                    style: theme.textTheme.labelSmall?.copyWith(
                      color: viewModel.isEnabled(item) ? colorScheme.onPrimaryContainer : colorScheme.onErrorContainer,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
