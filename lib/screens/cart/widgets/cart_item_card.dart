import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../common/constants/colors.dart';
import '../../../core/utils.dart';
import '../../../providers/cart_provider.dart';
import '../../../providers/data_provider.dart';

class CartDetailsItem extends StatefulWidget {
  final CartItem cartItem;
  final VoidCallback? onRemove;
  final VoidCallback? onAdd;
  final VoidCallback? onSubtract;

  const CartDetailsItem({super.key, required this.cartItem, this.onRemove, this.onAdd, this.onSubtract});

  @override
  State<CartDetailsItem> createState() => _CartDetailsItemState();
}

class _CartDetailsItemState extends State<CartDetailsItem> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: const Duration(milliseconds: 150), vsync: this);
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final DataProvider dataProvider = Provider.of<DataProvider>(context);
    final menu = dataProvider.menu;

    final addons = widget.cartItem.addons;
    final selectedAddons = addons.map((addon) => menu.getAddOnById(addon.refId)).toList();
    final selectedAddonsNames = selectedAddons.map((addon) => addon.name).toList();

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _isPressed ? AppColors.white.withAlpha(80) : AppColors.white.withAlpha(60),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: AppColors.black.withAlpha(15), width: 1),
                boxShadow: [
                  BoxShadow(color: AppColors.black.withAlpha(20), blurRadius: 10, offset: const Offset(0, 4)),
                ],
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Hero(
                    tag: widget.cartItem.item.name,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(color: AppColors.black.withAlpha(1), blurRadius: 8, offset: const Offset(0, 4)),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: widget.cartItem.item.imageId != null
                            ? CachedNetworkImage(
                                imageUrl: menu.getImageUrlById(widget.cartItem.item.imageId!),
                                height: 170,
                                width: double.infinity,
                                fit: BoxFit.cover,
                                errorWidget: (context, url, error) => Container(
                                  height: 170,
                                  color: Colors.grey[200],
                                  child: Icon(Icons.broken_image, size: 50, color: Colors.grey),
                                ),
                              )
                            : _buildPlaceholderImage(),
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Enhanced Item Details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          widget.cartItem.item.name,
                          style: const TextStyle(
                            fontSize: 25,
                            fontWeight: FontWeight.bold,
                            color: AppColors.black,
                            letterSpacing: -0.3,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        widget.cartItem.addons.isNotEmpty
                            ? Text(
                                selectedAddonsNames.join(', '),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.grey.shade500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              )
                            : SizedBox.shrink(),
                        const SizedBox(height: 8),

                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.green.withAlpha(50),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: AppColors.green.withAlpha(50), width: 1),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const SizedBox(width: 4),
                                  Text(
                                    '${widget.cartItem.count}x',
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                      color: AppColors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(width: 12),

                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.blue.withAlpha(30),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: AppColors.blue.withAlpha(30), width: 1),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Consumer<DataProvider>(
                                    builder: (context, dataProvider, _) {
                                      return Text(
                                        formatCurrencyWithRestaurant(
                                          widget.cartItem.price / widget.cartItem.count,
                                          dataProvider.hasRestaurant
                                              ? dataProvider.restaurant.restaurantSettings.currencyValue
                                              : null,
                                        ),
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: AppColors.blue,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 4),

                        // Total Price
                        Consumer<DataProvider>(
                          builder: (context, dataProvider, _) {
                            return Text(
                              'Total: ${formatCurrencyWithRestaurant(widget.cartItem.price, dataProvider.hasRestaurant ? dataProvider.restaurant.restaurantSettings.currencyValue : null)}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: const Color(0xFF2C3E50).withAlpha(80),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ),

                  // Enhanced Remove Button
                  if (widget.onRemove != null)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.red.withAlpha(10),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.red.withAlpha(20), width: 1),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: widget.onRemove,
                          borderRadius: BorderRadius.circular(12),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            child: Icon(Icons.delete_outline, color: Colors.red.shade600, size: 40),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Colors.grey[200]!, Colors.grey[300]!],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(child: Icon(Icons.restaurant, color: AppColors.lightGrey, size: 32)),
    );
  }
}
