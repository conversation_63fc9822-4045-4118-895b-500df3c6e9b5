import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart';

import '../common/env.dart';
import '../common/types.dart';
import '../data/enums/domain_category.dart';
import '../data/enums/menu_category.dart';
import '../models/add_on_item.dart';
import '../models/banner_image.dart';
import '../models/base.dart';
import '../models/emv.dart';
import '../models/food/food_category.dart';
import '../models/food/food_item.dart';
import '../models/food/food_sub_category.dart';
import '../models/order/order.dart';
import '../models/order/order_additional_charge.dart';
import '../models/order/order_item.dart';
import '../models/order/order_status.dart';
import '../models/payment_request_details.dart';
import '../models/restaurant/attachment.dart';
import '../models/restaurant/business_hours.dart';
import '../models/restaurant/restaurant.dart';
import '../models/restaurant/restaurant_info.dart';
import '../models/restaurant/restaurant_menu.dart';
import '../models/tax.dart';
import '../models/tip_charge.dart';
import '../models/user_profile.dart';
import '../providers/cart_provider.dart';
import 'base.dart';

part 'validator.mixin.dart';

const String baseUrl = "${Environment.apiEndpoint}/${Environment.apiVersion}";

const jsonHeaders = {HttpHeaders.contentTypeHeader: "application/json"};

class ApiManager extends ApiManagerBase with ApiResponseValidatorMixin {
  late final String? _token;

  @protected
  final Map<String, Order> _ordersMap = {};

  ApiManager() : _token = null;

  ApiManager.tokenInstance() {
    _token = getToken();
    if (_token == null) {
      throw "Token is null";
    }
  }

  Map<String, String> get _tokenHeaders => {
    HttpHeaders.authorizationHeader: "Bearer $_token",
    HttpHeaders.contentTypeHeader: "application/json",
  };

  Future<String?> getCurrentCountry() async {
    Uri uri = Uri.parse("http://ip-api.com/json");
    Response response = await client.get(uri);
    if (response.statusCode == 200) {
      return jsonDecode(response.body)["countryCode"];
    }

    throw "Failed to fetch country";
  }

  Future<RestaurantProfile> getRestaurantProfile(String pocId) async {
    Uri uri = Uri.parse("$baseUrl/users/$pocId");
    Response response = await client.get(uri, headers: _tokenHeaders);
    final data = validateJsonObject(response, onResponseNotOk: "Failed to fetch Restaurant Profile");

    debugPrint("ApiManager.getRestaurantProfile: ✅Fetched Restaurant Profile successfully");
    return RestaurantProfile(data);
  }

  Future<Order> clearTip(Order order, String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/clear/tip/${order.companyId}/${order.revenueCenterId}/${order.id}");

    Response response = await client.get(uri, headers: _tokenHeaders);
    validateJsonObject(response, onResponseNotOk: "Failed to clear tip");

    return await syncOrder(order, companyId, revenueCenterId);
  }

  Future<void> requestBill(String companyId, String revenueCenterId, String tableId) async {
    Uri uri = Uri.parse("$baseUrl/Notifications/$companyId/$revenueCenterId/$tableId/checkOut");
    Response response = await client.put(uri, headers: _tokenHeaders);
    validateJsonObject(response, onResponseNotOk: "Failed to Request Bill");
    debugPrint("ApiManager.requestBill: ✅Bill Requested Successfully");
  }

  Future<void> sendBill(String companyId, String revenueCenterId, String orderId) async {
    Uri uri = Uri.parse("$baseUrl/orders/sendBill/$companyId/$revenueCenterId/$orderId");
    Response response = await client.get(uri, headers: _tokenHeaders);
    validateJsonObject(response, onResponseNotOk: "Failed to send bill");
    debugPrint("ApiManager.sendBill: ✅Bill sent to Guest Successfully");
  }

  Future<EmvIterable> getEmvs(String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/payments/emv/list/$companyId/$revenueCenterId");
    Response response = await client.get(uri, headers: _tokenHeaders);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch EMVs");

    EmvList emvs = [];
    for (JSONObject item in data) {
      emvs.add(Emv(item));
    }

    debugPrint("ApiManager.getEmvs: ✅Fetched ${emvs.length} EMVs");
    return emvs;
  }

  Future<PaymentRequestDetails> getPaymentUrl(Restaurant restaurant, JSONObject request) async {
    String orderId = request["orderId"];
    Uri uri = Uri.parse("$baseUrl/payments/${restaurant.companyId}/${restaurant.revenueCenterId}/$orderId");
    request.remove('orderId');
    Response response = await client.post(uri, headers: _tokenHeaders, body: jsonEncode(request));

    final data = validateJsonObject(response, onResponseNotOk: "Failed to get Payment Request Details");

    debugPrint("ApiManager.getPaymentUrl: ✅Payment Request Details fetched successfully");
    return PaymentRequestDetails(data);
  }

  Future<PaymentRequestDetails> verifyPayment(
    Restaurant restaurant,
    PaymentRequestDetails requestDetails,
    JSONObject body,
    bool isTestMode,
  ) async {
    Uri uri = Uri.parse(
      "$baseUrl/payments/${restaurant.companyId}/${restaurant.revenueCenterId}/${requestDetails.orderId}/${requestDetails.id}",
    );

    Response response = await client.put(uri, body: jsonEncode(body), headers: _tokenHeaders);

    final data = validateJsonObject(response, onResponseNotOk: "Failed to verify Payment Request");

    debugPrint("ApiManager.verifyPayment: ✅Payment Request verified successfully");
    return PaymentRequestDetails(data);
  }

  Future<PaymentRequestDetails> checkPaymentStatus(String paymentId) async {
    Uri uri = Uri.parse("$baseUrl/payments/$paymentId");

    Response response = await client.get(uri, headers: _tokenHeaders);

    final data = validateJsonObject(response, onResponseNotOk: "Failed to check Payment Status");

    debugPrint("ApiManager.checkPaymentStatus: ✅Payment Status checked successfully");
    return PaymentRequestDetails(data);
  }

  //   Get All users
  //Domain APi
  Uri _getDomainUriFor(CompanyDomainCategory category) {
    return Uri.parse("$baseUrl/domainValues/company/${DomainValues.companyMasterId}/category/${category.id}");
  }

  Uri _getRVCSpecificDomainValuesUri(String rvcId, RvcDomainCategory category) {
    return Uri.parse("$baseUrl/domainValues/company/$rvcId?categoryId=${category.id}&includeDisabled=false");
  }

  //menu Categories API

  final FoodCategoriesList categories = [];
  final FoodSubCategoriesList subCategories = [];
  final FoodItemsList foodItems = [];
  final AddOnItemList addOnItems = [];
  final Map<String, String> images = {};
  final TaxList taxValues = [];
  final List<TipCharge> tipCharges = [];

  Future<void> _getAllCategoryItems(String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/domainValues/company/$revenueCenterId/category/${MenuCategory.category.id}");
    Response response = await client.get(uri);

    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Menu Categories");

    for (JSONObject item in data) {
      categories.add(FoodCategory(item));
    }

    debugPrint("MenuApis._getAllCategoryItems: ✅: Fetched ${categories.length} Categories");
  }

  Future<void> _getAllSubCategoryItems(String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/domainValues/company/$revenueCenterId/category/${MenuCategory.subCategory.id}");
    Response response = await client.get(uri);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Menu Sub-Categories");

    for (JSONObject item in data) {
      subCategories.add(FoodSubCategory(item));
    }

    debugPrint("MenuApis._getAllSubCategoryItems: ✅MENU-API: Fetched ${subCategories.length} Sub-Categories");
  }

  Future<void> _getAllMenuItems(String companyId, String revenueCenterId, String orderTypeId, String timezoneId) async {
    String timezone = TimezoneValues.byId(timezoneId);

    String url =
        "$baseUrl/inventories/$companyId/$revenueCenterId/"
        "?modeId=$orderTypeId"
        "&dayTime=${DateTime.now().toIso8601String()}"
        "&delete=false"
        "&timeZone=$timezone";

    Uri uri = Uri.parse(url);
    Response response = await client.get(uri);

    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Menu Items");

    for (JSONObject item in data) {
      foodItems.add(FoodItem(item));
    }

    debugPrint("MenuApis._getAllMenuItems: ✅Fetched ${foodItems.length} Food Items");
  }

  Future<void> _getAllInventoryItems(String companyId, String revenueCenterId, String orderTypeId) async {
    String url =
        "$baseUrl/inventories/$companyId/$revenueCenterId/"
        "?modeId=$orderTypeId";

    Uri uri = Uri.parse(url);
    Response response = await client.get(uri);

    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Inventory Items");

    for (JSONObject item in data) {
      foodItems.add(FoodItem(item));
    }

    debugPrint("MenuApis._getAllInventoryItems: ✅Fetched ${foodItems.length} Food Items");
  }

  String _getAddOnUrl(String categoryId, String revenueCenterId) =>
      ""
      "$baseUrl/domainValues/company/$revenueCenterId"
      "?categoryId=$categoryId"
      "&includeDisabled=true";

  Future<void> _getAllAddOnItems(String revenueCenterId) async {
    Uri choiceUri = Uri.parse(_getAddOnUrl(MenuCategory.choices.id, revenueCenterId));
    Uri customizationUri = Uri.parse(_getAddOnUrl(MenuCategory.customizations.id, revenueCenterId));

    List<Response> responses = await Future.wait([client.get(choiceUri), client.get(customizationUri)]);

    final choicesData = validateJsonList(responses.first, onResponseNotOk: "Failed to fetch Choices");

    final customizationsData = validateJsonList(responses.last, onResponseNotOk: "Failed to fetch Customizations");

    List allItemsJson = [...choicesData, ...customizationsData];
    for (JSONObject item in allItemsJson) {
      addOnItems.add(AddOnItem(item));
    }

    debugPrint("MenuApis._getAllAddOnItems: ✅Fetched ${addOnItems.length} Add-On Items");
  }

  Future<void> _getImages(String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/attachments/company/$revenueCenterId");
    Response response = await client.get(uri);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch image attachments");

    for (JSONObject item in data) {
      String? image = item["url"];
      if (image != null) {
        images[item["_id"]] = image;
      }
    }

    debugPrint("MenuApis._getImages: ✅Fetched ${images.length} Image Attachments");
  }

  Future<void> _getTaxDetails(String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/domainValues/company/$revenueCenterId/category/${MenuCategory.tax.id}");
    Response response = await client.get(uri);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Tax Details");
    for (JSONObject item in data) {
      taxValues.add(Tax(item));
    }

    debugPrint("MenuApis._getTaxDetails: ✅Fetched ${taxValues.length} Tax Details");
  }

  Future<void> _getTipCharges(String revenueCenterId) async {
    Uri uri = Uri.parse(
      "$baseUrl/domainValues/company/$revenueCenterId"
      "?categoryId=${MenuCategory.tipCharge.id}"
      "&includeDisabled=false",
    );
    Response response = await client.get(uri);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Tip Charges");
    for (JSONObject item in data) {
      tipCharges.add(TipCharge(item));
    }

    debugPrint("MenuApis._getTipCharges: ✅Fetched ${tipCharges.length} Tip Charges");
  }

  // Updated getMenu to take named parameters
  Future<RestaurantMenu> getMenu({
    required String companyId,
    required String revenueCenterId,
    required String orderTypeId,
    required String timezoneId,
  }) async {
    try {
      await Future.wait([
        _getAllCategoryItems(revenueCenterId),
        _getAllSubCategoryItems(revenueCenterId),
        _getAllMenuItems(companyId, revenueCenterId, orderTypeId, timezoneId),
        _getAllAddOnItems(revenueCenterId),
        _getImages(revenueCenterId),
        _getTaxDetails(revenueCenterId),
        _getTipCharges(revenueCenterId),
      ]);
    } catch (e, st) {
      debugPrint("MenuApis.getMenu: ❌ERROR: MENU-API $e\n$st");
      rethrow;
    }

    return RestaurantMenu(
      categories: categories,
      subCategories: subCategories,
      foodItems: foodItems,
      addOnItems: addOnItems,
      imagesMap: images,
      taxValues: taxValues,
      tipCharges: tipCharges,
    );
  }

  Future<RestaurantMenu> getAllInventory({required String companyId, required String revenueCenterId, required String orderTypeId}) async {
    try {
      Future.wait([
        _getAllCategoryItems(revenueCenterId),
        _getAllSubCategoryItems(revenueCenterId),
        _getAllInventoryItems(companyId, revenueCenterId, orderTypeId),
        _getAllAddOnItems(revenueCenterId),
        _getImages(revenueCenterId),
        _getTaxDetails(revenueCenterId),
        _getTipCharges(revenueCenterId),
      ]);
    } catch (e, st) {
      debugPrint("MenuApis.getAllInventory: ❌ERROR: MENU-API $e\n$st");
      rethrow;
    }
    debugPrint("MenuApis.getAllInventory: ✅Fetched Inventory successfully");
    return RestaurantMenu(
      categories: categories,
      subCategories: subCategories,
      foodItems: foodItems,
      addOnItems: addOnItems,
      imagesMap: images,
      taxValues: taxValues,
      tipCharges: tipCharges,
    );
  }

  Future<void> _getData({required Uri uri, required Function(JSONList jsonList) then}) async {
    Response response = await client.get(uri);

    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch $uri");
    then.call(data);
  }

  Future loadAdditionalChargeValues(String rvcId) async {
    Uri uri = _getRVCSpecificDomainValuesUri(rvcId, RvcDomainCategory.additionalCharges);
    Response response = await client.get(uri);

    final data = validateJsonList(response, onResponseNotOk: "Failed to load Additional Charges");
    DomainValues.loadAdditionalChargesValues(data);
  }

  Future loadAutoChargeValues(String rvcId) async {
    Uri uri = _getRVCSpecificDomainValuesUri(rvcId, RvcDomainCategory.autoCharges);
    Response response = await client.get(uri);

    final data = validateJsonList(response, onResponseNotOk: "Failed to load Auto Charges");
    DomainValues.loadAutoChargesValues(data);
  }

  Future loadTableTypeCategoryValues(String rvcId) async {
    Uri uri = _getRVCSpecificDomainValuesUri(rvcId, RvcDomainCategory.tableTypeCategory);
    Response response = await client.get(uri);

    final data = validateJsonList(response, onResponseNotOk: "Failed to load Table Type Categories");
    DomainValues.loadTableTypeCategories(data);
  }

  Future loadDomainValues() async {
    Uri currencyValuesUri = _getDomainUriFor(CompanyDomainCategory.currency),
        dateFormatValuesUri = _getDomainUriFor(CompanyDomainCategory.dateFormat),
        timeFormatValuesUri = _getDomainUriFor(CompanyDomainCategory.timeFormat),
        timezoneValuesUri = _getDomainUriFor(CompanyDomainCategory.timezone);

    try {
      await Future.wait([
        _getData(uri: currencyValuesUri, then: (jsonList) => DomainValues.loadCurrencyValues(jsonList)),
        _getData(uri: dateFormatValuesUri, then: (jsonList) => DomainValues.loadDateFormatValues(jsonList)),
        _getData(uri: timeFormatValuesUri, then: (jsonList) => DomainValues.loadTimeFormatValues(jsonList)),
        _getData(uri: timezoneValuesUri, then: (jsonList) => DomainValues.loadTimezoneValues(jsonList)),
      ]);
      debugPrint("DomainValuesApis.loadDomainValues: ✅Domain values loaded");
    } catch (e, st) {
      debugPrint("DomainValuesApis.loadDomainValues: ❌ERROR: $e\n$st");
      rethrow;
    }
  }

  //Orders API

  Future<Order> syncOrder(Order order, String companyId, String revenueCenterId) async {
    final uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId/${order.id}");
    final response = await client.get(uri, headers: _tokenHeaders);

    final data = validateJsonObject(response, onResponseNotOk: "Failed to sync Order(${order.id})");

    final updatedOrder = Order(data);
    _ordersMap[order.id] = updatedOrder;

    return updatedOrder;
  }

  Future<OrdersIterable> getGuestOrders({
    String? phone,
    String? email,
    required String companyId,
    required String revenueCenterId,
  }) async {
    debugPrint("OrderApis.getGuestOrders: 📂Fetching Guest Orders");
    if (phone == null && email == null) {
      throw "email and phone cannot be null";
    }

    Uri uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId");
    Uri newUri = Uri(
      scheme: uri.scheme,
      host: uri.host,
      path: uri.path,
      queryParameters: {"emailPhone": phone ?? email},
    );
    return getOrders(newUri, companyId, revenueCenterId);
  }

  Future<OrdersIterable> getRunnerOrders(String orderTypeId, String companyId, String revenueCenterId) async {
    debugPrint("OrderApis.getRunnerOrders: 📂Fetching Runner Orders");
    Uri uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId?orderTypeId=$orderTypeId");
    return getOrders(uri, companyId, revenueCenterId);
  }

  Future<OrdersIterable> getOrders(Uri uri, String companyId, String revenueCenterId) async {
    final response = await client.get(uri, headers: _tokenHeaders);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch orders");

    for (final itemJson in data) {
      final order = Order(itemJson);
      _ordersMap[order.id] = order;
    }

    if (_ordersMap.isNotEmpty) {
      await _getOrderItems(companyId, revenueCenterId);
    }

    return _ordersMap.values;
  }

  Future<void> _getOrderItemsOfId(String orderId, String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId/items/order/$orderId");
    Response response = await client.get(uri, headers: _tokenHeaders);
    OrderItemsList orderItems = [];
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch items of Order($orderId)");

    for (Map<String, dynamic> item in data) {
      OrderItem orderItem = OrderItem(item);
      orderItems.add(orderItem);
    }

    _ordersMap[orderId]!.setItems(orderItems);
  }

  Future<void> _getAdditionalCharges(String orderId, String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/additional-charges/$companyId/$revenueCenterId/$orderId");
    Response response = await client.get(uri, headers: _tokenHeaders);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Additional Charges for Order($orderId)");

    AdditionalChargeList additionalCharges = [];
    for (JSONObject item in data) {
      OrderAdditionalCharge additionalCharge = OrderAdditionalCharge(item);
      additionalCharges.add(additionalCharge);
    }
    _ordersMap[orderId]!.setAdditionalCharges(additionalCharges);
  }

  Future<void> _getOrderItems(String companyId, String revenueCenterId, {int batchSize = 10}) async {
    final orderIds = _ordersMap.keys.toList();

    for (int i = 0; i < orderIds.length; i += batchSize) {
      final batch = orderIds.skip(i).take(batchSize);

      await Future.wait(
        batch.map((orderId) async {
          try {
            await Future.wait([
              _getOrderItemsOfId(orderId, companyId, revenueCenterId),
              _getAdditionalCharges(orderId, companyId, revenueCenterId),
            ]);
          } catch (e, st) {
            debugPrint("Failed to fetch items or charges for orderId=$orderId: $e\n$st");
          }
        }),
      );
    }
  }

  Future<Order> getOrderById(String id, String companyId, String revenueCenterId) async {
    final uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId/$id");
    final response = await client.get(uri, headers: _tokenHeaders);
    final data = validateJsonObject(response, onResponseNotOk: "Failed to fetch Order($id)");

    final order = Order(data);
    _ordersMap[order.id] = order;
    await _getOrderItems(companyId, revenueCenterId);

    return _ordersMap.values.first;
  }

  Future<Order> getOrder(Order order, String companyId, String revenueCenterId) async {
    _ordersMap[order.id] = order;
    await _getOrderItems(companyId, revenueCenterId);

    return _ordersMap.values.first;
  }

  Future<void> addAdditionalCharges({
    required String orderId,
    required String typeName,
    required String typeId,
    required double charges,
    required String companyId,
    required String revenueCenterId,
  }) async {
    Uri uri = Uri.parse("$baseUrl/additional-charges/$companyId/$revenueCenterId/$orderId");
    debugPrint("OrderApis.addAdditionalCharges: 🐞$typeId");
    final body = {
      // "orderId": orderId,
      "typeName": typeName,
      "type": typeId,
      // "valueTypes": "AMOUNT",
      "amount": charges,
    };

    Response response = await post(uri, body: jsonEncode(body), headers: _tokenHeaders);

    validateJsonObject(response, onResponseNotOk: "Failed to add Additional Charges");
  }

  Future<void> _addAutoAdditionalCharges(String companyId, String revenueCenterId, String orderId) async {
    Uri uri = Uri.parse("$baseUrl/additional-charges/auto/$companyId/$revenueCenterId/$orderId");
    Response response = await client.get(uri, headers: _tokenHeaders);

    validateJsonObject(response, onResponseNotOk: "Failed to add Additional Charges");
  }

  Future<Order> placeOrder(JSONObject request, String companyId, String revenueCenterId) async {
    final uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId");
    final response = await client.post(uri, headers: _tokenHeaders, body: jsonEncode(request));

    final data = validateJsonObject(response, onResponseNotOk: "Failed to create Order");

    final order = Order(data);

    await _addAutoAdditionalCharges(companyId, revenueCenterId, order.id);

    return getOrder(order, companyId, revenueCenterId);
  }

  Future<Order> addKotToOrder(String orderId, String companyId, String revenueCenterId, JSONObject request) async {
    Uri uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId/$orderId");
    Response response = await client.post(uri, headers: _tokenHeaders, body: jsonEncode(request));

    final data = validateJsonObject(response, onResponseNotOk: "Failed to Add Kot to Order");

    debugPrint("OrderApis.addKotToOrder: ✅Added Kot to Order");
    return Order(data);
  }

  Future<void> _updateOrderStatus(
    String orderId,
    String statusId,
    String companyId,
    String revenueCenterId, [
    JSONObject? body,
  ]) async {
    Uri uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId/$orderId/status/$statusId");
    String? bodyString = body != null ? jsonEncode(body) : null;
    Response response = await client.put(uri, headers: _tokenHeaders, body: bodyString);

    validateJsonObject(response, onResponseNotOk: "Failed to update order status");
  }

  Future<void> _updateStatusByKot(
    String orderId,
    int kotNo,
    String statusId,
    String companyId,
    String revenueCenterId, [
    JSONObject? body,
  ]) async {
    Uri uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId/$orderId/kotstatus/$kotNo/$statusId");
    String? bodyJson = body != null ? jsonEncode(body) : null;
    Response response = await client.put(uri, headers: _tokenHeaders, body: bodyJson);
    validateJsonObject(response, onResponseNotOk: "Failed to Update Kot($kotNo) status");
  }

  Future<void> _updateItemStatus(
    String orderId,
    String itemId,
    String statusId,
    String companyId,
    String revenueCenterId, [
    JSONObject? body,
  ]) async {
    Uri uri = Uri.parse("$baseUrl/orders/$companyId/$revenueCenterId/$orderId/$itemId/status/$statusId");
    String? jsonEncodedBody = body != null ? jsonEncode(body) : null;
    Response response = await client.put(uri, headers: _tokenHeaders, body: jsonEncodedBody);

    validateJsonObject(
      response,
      onResponseNotOk: "Failed to update item status ($orderId, ${OrderStatus.fromId(statusId)})",
    );
  }

  Future<void> markAsCompleted(Order order, String companyId, String revenueCenterId) async {
    final pendingItems = order.items.where((item) => item.status == OrderStatus.pending);

    await Future.wait(
      pendingItems.map(
        (item) => _updateItemStatus(order.id, item.id, OrderStatus.completed.id, companyId, revenueCenterId),
      ),
    );

    await _updateOrderStatus(order.id, OrderStatus.completed.id, companyId, revenueCenterId);
  }

  Future<void> cancelOrder(Order order, String reason, String companyId, String revenueCenterId) async {
    await Future.wait([
      ...order.kotNos.map(
        (kot) =>
            _updateStatusByKot(order.id, kot, OrderStatus.cancelled.id, companyId, revenueCenterId, {"reason": reason}),
      ),
      _updateOrderStatus(order.id, OrderStatus.cancelled.id, companyId, revenueCenterId, {"reason": reason}),
    ]);
  }

  Future<Order> approveAllKotItems(Order order, String companyId, String revenueCenterId) async {
    await Future.wait(
      order.kotNos.map(
        (kotNo) => _updateStatusByKot(order.id, kotNo, OrderStatus.approved.id, companyId, revenueCenterId),
      ),
    );

    await _updateOrderStatus(order.id, OrderStatus.approved.id, companyId, revenueCenterId);
    return await getOrderById(order.id, companyId, revenueCenterId);
  }

  Future<Order> declineByKotNo(
    Order order,
    int kotNo,
    OrderItemsIterable orderItems,
    String reason,
    String companyId,
    String revenueCenterId,
  ) async {
    final body = {"reason": reason, "items": orderItems.map((e) => e.id).toList()};

    await _updateStatusByKot(order.id, kotNo, OrderStatus.declined.id, companyId, revenueCenterId, body);
    return await getOrderById(order.id, companyId, revenueCenterId);
  }

  Future<Order> modifyOrder(
    String orderId,
    JSONObject addItemRequest,
    OrderItemsIterable itemsToApprove,
    List<CartItem> itemsToModify,
    int kotNo,
    String companyId,
    String revenueCenterId,
  ) async {
    if (addItemRequest.isNotEmpty) {
      await addKotToOrder(orderId, companyId, revenueCenterId, addItemRequest);
    }

    if (itemsToModify.isNotEmpty) {
      String modifyItemBaseUrl = "$baseUrl/orders/$companyId/$revenueCenterId/$orderId";

      Future<void> updateItemCount(CartItem item) async {
        final uri = Uri.parse("$modifyItemBaseUrl/${item.orderItemId!}/quantity/${item.count}");
        final response = await client.put(uri, headers: _tokenHeaders);
        validateJsonObject(response, onResponseNotOk: "Failed to update count for Item(${item.orderItemId})");
      }

      await Future.wait(itemsToModify.map((e) => updateItemCount(e)));
    }

    if (itemsToApprove.isNotEmpty) {
      await _updateStatusByKot(orderId, kotNo, OrderStatus.approved.id, companyId, revenueCenterId);
    }

    return await getOrderById(orderId, companyId, revenueCenterId);
  }

  //   Restaurants API

  Future<RestaurantInfo> getRestaurantDetailsFromRVCId(String rvcId, String companyId) async {
    Uri uri = Uri.parse("$baseUrl/companies/$rvcId");
    Response response = await client.get(uri);
    final data = validateJsonObject(response, onResponseNotOk: "Failed to fetch Restaurant Details");
    final restaurantInfo = RestaurantInfo(data);
    debugPrint("RestaurantApis.getRestaurantDetailsFromRVCId: ✅Fetched ${restaurantInfo.name} Details");
    if (restaurantInfo.logoId.isNotEmpty) {
      final attachment = await getAttachment(companyId, restaurantInfo.logoId);
      restaurantInfo.logoUrl = attachment.url;
    }
    return restaurantInfo;
  }

  Future<Attachment> getAttachment(String companyId, String attachmentId) async {
    Uri uri = Uri.parse("$baseUrl/attachments/company/$companyId/$attachmentId");
    Response response = await client.get(uri);
    final data = validateJsonObject(response, onResponseNotOk: "Failed to fetch Attachment");
    return Attachment(data);
  }

  Future<JSONList> _getCompanySettings(String companyId) async {
    Uri uri = Uri.parse("$baseUrl/settings/company/$companyId/Company/$companyId/SETTINGS");
    Response response = await client.get(uri);
    return validateJsonList(response, onResponseNotOk: "Failed to fetch Company Settings");
  }

  Future<JSONList> _getRestaurantSettings(String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/settings/company/$companyId/Company/$revenueCenterId/SETTINGS");
    Response response = await client.get(uri);
    return validateJsonList(response, onResponseNotOk: "Failed to fetch Restaurant Settings");
  }

  Future<JSONList> _getRestaurantPaymentSettings(String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/settings/company/$companyId/Company/$revenueCenterId/PAYMENT_SETTINGS");
    Response response = await client.get(uri);
    return validateJsonList(response, onResponseNotOk: "Failed to fetch Restaurant Payment Settings");
  }

  Future<Restaurant> getRestaurantDetails(String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/companies/$companyId");
    Response response = await client.get(uri);
    final data = validateJsonObject(response, onResponseNotOk: "Failed to fetch Restaurant Details");

    List<JSONList> responses = await Future.wait([
      _getCompanySettings(companyId),
      _getRestaurantSettings(companyId, revenueCenterId),
      _getRestaurantPaymentSettings(companyId, revenueCenterId),
    ]);

    JSONList companySettings = responses[0];
    JSONList restaurantSettings = responses[1];
    JSONList paymentSettings = responses[2];
    Restaurant restaurant = Restaurant(
      data,
      revenueCenterId,
      companySettings: companySettings,
      restaurantSettings: restaurantSettings,
      paymentSettings: paymentSettings,
    );

    debugPrint("RestaurantApis.getRestaurantDetails: ✅Fetched Restaurant Details");
    return restaurant;
  }

  Future<BusinessDaysIterable> getBusinessHours(String companyId, String revenueCenterId) async {
    Uri businessHoursUri = Uri.parse("$baseUrl/businessHours/$companyId/$revenueCenterId");
    Response response = await get(businessHoursUri);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Business Hours");

    BusinessDaysList businessHours = [];
    for (JSONObject json in data) {
      businessHours.add(BusinessDays(json));
    }

    debugPrint("RestaurantApis.getBusinessHours: ✅Fetched ${businessHours.length} Business Hours");
    return businessHours;
  }

  Future<BannerImageIterable> getBannerImages(String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/promotions/$companyId/$revenueCenterId");
    Response response = await client.get(uri);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Banner Images");

    BannerImageList bannerImages = [];
    for (JSONObject json in data) {
      bannerImages.add(BannerImage(json));
    }

    debugPrint("RestaurantApis.getBannerImages: ✅Fetched ${bannerImages.length} Banner Images");
    return bannerImages;
  }

  /// Get domain values for payment gateways
  Future<JSONList> getPaymentGatewayDomainValues(String companyId) async {
    Uri uri = Uri.parse(
      "$baseUrl/domainValues/company/${Environment.masterCompanyId}?categoryId=${GlobalDomainCategory.paymentGateways.id}",
    );
    Response response = await client.get(uri);
    final data = validateJsonList(response, onResponseNotOk: "Failed to fetch Payment Gateway Domain Values");

    debugPrint("ApiManager.getPaymentGatewayDomainValues: ✅Fetched ${data.length} Payment Gateway Domain Values");
    return data;
  }

  Future<void> login(String email, String password) async {
    Uri uri = Uri.parse("$baseUrl/auth/login");
    final body = {"email": email, "password": password};
    Response response = await client.post(uri, headers: jsonHeaders, body: jsonEncode(body));

    final data = validateJsonObject(response, onResponseNotOk: "Failed to Login");

    String token = data["token"]!;
    saveToken(token);
    debugPrint("AuthApis.login: ✅Login Successful: $email");
  }

  Future<UserProfile> getUserProfile({required bool isEmployee, required String fcmToken}) async {
    Uri uri = Uri.parse("$baseUrl/auth/profile?token=$fcmToken");
    var response = await client.get(uri, headers: _tokenHeaders);
    final data = validateJsonObject(response, onResponseNotOk: "Failed to fetch User Profile");

    UserProfile userProfile = isEmployee ? UserProfile.employee(data) : UserProfile.guest(data);
    debugPrint("ApiManager.getUserProfile: ✅User Profile fetched successfully");
    return userProfile;
  }

  Future<String> getPrintBillContent(String orderId, String companyId, String revenueCenterId) async {
    Uri uri = Uri.parse("$baseUrl/orders/printBill/$companyId/$revenueCenterId/$orderId");
    var response = await client.get(uri, headers: _tokenHeaders);

    if (response.statusCode != 200) {
      throw "Failed to fetch Print Bill Content";
    }

    String content = jsonDecode(response.body)["data"];
    debugPrint("ApiManager.getPrintBillContent: ✅Print Bill Content fetched successfully");
    return content;
  }
}
