import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../../common/constants/constants.dart';
import '../../models/order/order.dart';
import '../../models/payment_request_details.dart';
import '../../models/restaurant/restaurant.dart';
import '../../screens/auth/login_screen.dart';
import '../../screens/cart/cart_screen.dart';
import '../../screens/home/<USER>';
import '../../screens/menu/menu_screen.dart';
import '../../screens/order/order_confirmation_screen.dart';
import '../../screens/payments/emv_payment_screen.dart';
import '../../screens/payments/payment_failed_screen.dart';
import '../../screens/payments/payment_webview_screen.dart';
import '../../screens/setup/setup_emv/setup_emv.dart';
import '../../screens/setup/setup_printer/setup_printer.dart';
import '../../screens/setup/setup_menu/setup_menu.dart';
import '../../screens/setup/setup_menu/setup_menu.vm.dart';

BuildContext? get globalNavigatorState => globalNavigatorKey.currentContext;

GoRouter getRouterConfig(String initialRoute) {
  return GoRouter(
    initialLocation: initialRoute,
    navigatorKey: globalNavigatorKey,
    routes: [
      GoRoute(path: '/', builder: (context, state) => const LoginScreen()),
      GoRoute(path: '/home', builder: (context, state) => const HomeScreen()),
      GoRoute(path: '/menu', builder: (context, state) => const MenuScreen()),
      GoRoute(path: '/cart', builder: (context, state) => const CartDetailsScreen()),
      GoRoute(path: "/printer/setup", builder: (context, state) => const SetupPrinter()),
      GoRoute(path: "/emv/setup", builder: (context, state) => const SetupEmvScreen()),
      // Payment routes
      GoRoute(
        path: '/payment-webview',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return PaymentWebViewScreen(
            paymentDetails: extra['paymentDetails'] as PaymentRequestDetails,
            restaurant: extra['restaurant'] as Restaurant,
            order: extra['order'] as Order,
          );
        },
      ),
      GoRoute(
        path: '/emv-payment',
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>;
          return EmvPaymentScreen(
            paymentDetails: extra['paymentDetails'] as PaymentRequestDetails,
            restaurant: extra['restaurant'] as Restaurant,
            order: extra['order'] as Order,
          );
        },
      ),

      // Order confirmation route (for successful orders)
      GoRoute(
        path: '/order-confirmation',
        builder: (context, state) {
          final order = state.extra as Order;
          return OrderConfirmationScreen(order: order);
        },
      ),

      // Payment failed route
      GoRoute(
        path: '/payment-failed',
        builder: (context, state) {
          final extra = state.extra;
          String? errorMessage;
          Order? order;
          Restaurant? restaurant;

          if (extra is Map<String, dynamic>) {
            errorMessage = extra['errorMessage'] as String? ?? extra['error'] as String?;
            order = extra['order'] as Order?;
            restaurant = extra['restaurant'] as Restaurant?;
          } else {
            errorMessage = extra as String?;
          }

          return PaymentFailedScreen(errorMessage: errorMessage, order: order, restaurant: restaurant);
        },
      ),

      GoRoute(
        path: '/setup-menu',
        builder: (context, state) =>
            ChangeNotifierProvider(create: (_) => SetupMenuViewModel(), child: const SetupMenu()),
      ),
    ],
  );
}
