import 'dart:convert';

import '../common/globals.dart';
import 'package:flutter/material.dart';
import 'package:flutter_thermal_printer/utils/printer.dart' show Printer;

import '../api/api_manager.dart';
import '../core/utils.dart';
import '../models/order/order_type.dart';
import '../models/restaurant/restaurant.dart';
import '../models/restaurant/restaurant_info.dart';
import '../models/restaurant/restaurant_menu.dart';

class DataProvider extends ChangeNotifier {
  RestaurantMenu? _menu;

  bool _isLoadingMenu = false;

  bool get isMenuLoading => _isLoadingMenu;

  bool get hasMenu => _menu != null;

  bool _isRestaurantLoading = false;

  bool get isRestaurantLoading => _isRestaurantLoading;

  bool _hasRestaurant = false;

  bool get hasRestaurant => _hasRestaurant;

  RestaurantMenu get menu => _menu!;

  Restaurant? _restaurant;

  Restaurant get restaurant => _restaurant!;

  RestaurantInfo? _restaurantInfo;

  RestaurantInfo get restaurantInfo => _restaurantInfo!;

  String? _companyId;
  String? _revenueCenterId;

  String? get companyId => _companyId;

  String? get revenueCenterId => _revenueCenterId;

  set companyId(String? companyId) {
    _companyId = companyId;
    notifyListeners();
  }

  set revenueCenterId(String? revenueCenterId) {
    _revenueCenterId = revenueCenterId;
    notifyListeners();
  }

  Future<void> loadRestaurant(BuildContext context) async {
    _isRestaurantLoading = true;
    notifyListeners();
    ApiManager apiManager = ApiManager();
    await apiManager.loadDomainValues();

    if (_companyId == null || _revenueCenterId == null) {
      _isRestaurantLoading = false;
      notifyListeners();
      showErrorSnackBar(context, "Company ID or Revenue Center ID is null");
      return;
    }

    _restaurant = await apiManager.getRestaurantDetails(_companyId!, _revenueCenterId!);
    _restaurantInfo = await apiManager.getRestaurantDetailsFromRVCId(_revenueCenterId!, _companyId!);
    _hasRestaurant = true;
    _isRestaurantLoading = false;
    notifyListeners();
  }

  Future<void> loadMenu(Restaurant restaurant, OrderType orderType) async {
    _isLoadingMenu = true;
    notifyListeners();

    debugPrint("DataProvider.loadMenu 🐞, restaurant ${restaurant.restaurantSettings.timezoneValue}");

    try {
      ApiManager apiManager = ApiManager();
      _menu = await apiManager.getMenu(
        companyId: restaurant.companyId,
        revenueCenterId: restaurant.revenueCenterId,
        orderTypeId: orderType.id,
        timezoneId: restaurant.restaurantSettings.timezoneValue,
      );
    } catch (e) {
      debugPrint("DataProvider.loadMenu: ❌ERROR: $e");
    }

    _isLoadingMenu = false;
    notifyListeners();
  }

  Future<void> addBillPrinter(BuildContext context, Printer printer) async {
    if (_restaurant == null) return;
    String? printerName = printer.name;
    if(printerName == null) {
      showErrorSnackBar(context, "Printer name is null");
      return;
    }

    sharedPreferences.setString("printer", jsonEncode(printer));
    
    
    showSnackBar(context, "Adding printer $printerName...");
  }
}
