import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../api/api_manager.dart';
import '../api/base.dart';
import '../core/utils.dart';
import '../widgets/loading_dialog.dart';
import 'data_provider.dart';

class AuthProvider extends ChangeNotifier {
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();

  AuthProvider() {
    if (kDebugMode) {
      emailController.text = "<EMAIL>";
      passwordController.text = "Test@123";
    }
    // Check if already logged in (persistent session)
    _isLoggedIn = hasValidSession();
  }

  bool _isLoggedIn = false;

  bool get isLoggedIn => _isLoggedIn;

  /// Check if there's a valid session (token exists)
  bool hasValidSession() {
    final ApiManager apiManager = ApiManager();
    final token = apiManager.getToken();
    return token != null && token.isNotEmpty;
  }

  String? _loadingMessage;

  String? get loadingMessage => _loadingMessage;

  void login(BuildContext context) async {
    LoadingDialog loadingDialog = LoadingDialog(context, "Logging in...");
    loadingDialog.show();
    _loadingMessage = "Logging in...";
    notifyListeners();

    try {
      final ApiManager apiManager = ApiManager();
      if (emailController.text.isEmpty || passwordController.text.isEmpty) {
        _loadingMessage = null;
        notifyListeners();
        showErrorSnackBar(context, "Email or password cannot be empty");
        return;
      }
      await apiManager.login(emailController.text, passwordController.text);
      loadingDialog.dismiss();
      if (context.mounted) {
        getProfile(context);
      }
    } catch (e) {
      showErrorSnackBar(context, "Failed to login: $e");
    }
    loadingDialog.dismiss();
    _loadingMessage = null;
    _isLoggedIn = true;
    notifyListeners();
  }

  Future<void> checkLogin(BuildContext context) async {
    LoadingDialog loadingDialog = LoadingDialog(context, "Logging in...");
    loadingDialog.show();
    _loadingMessage = "Logging in...";
    notifyListeners();

    try {
      final ApiManager apiManager = ApiManager();
      if (emailController.text.isEmpty || passwordController.text.isEmpty) {
        _loadingMessage = null;
        notifyListeners();
        showErrorSnackBar(context, "Email or password cannot be empty");
        return;
      }
      await apiManager.login(emailController.text, passwordController.text);
      loadingDialog.dismiss();
    } catch (e) {
      showErrorSnackBar(context, "Failed to login: $e");
    }
    loadingDialog.dismiss();
    _loadingMessage = null;
    _isLoggedIn = true;
    notifyListeners();
  }

  void getProfile(BuildContext context) async {
    LoadingDialog loadingDialog = LoadingDialog(context, "Loading Profile...");
    _loadingMessage = "Loading Profile...";
    loadingDialog.show();
    notifyListeners();

    try {
      ApiManager apiManager = ApiManager.tokenInstance();
      final userProfile = await apiManager.getUserProfile(isEmployee: true, fcmToken: "");

      final DataProvider dataProvider = Provider.of<DataProvider>(context, listen: false);
      dataProvider.companyId = userProfile.companyId;
      dataProvider.revenueCenterId = userProfile.rvcId;

      loadingDialog.dismiss();
      loadingDialog = LoadingDialog(context, "Loading Restaurant...");
      loadingDialog.show();
      notifyListeners();

      await dataProvider.loadRestaurant(context);

      loadingDialog.dismiss();
      _loadingMessage = null;
      notifyListeners();

      if (context.mounted) {
        context.go('/home');
      }
    } catch (e, st) {
      loadingDialog.dismiss();
      _loadingMessage = null;
      if (e.toString().contains("Unauthorized")) {
        logout();
        context.go("/");
      }
      notifyListeners();
      debugPrint('AuthProvider.getProfile: Error: $e, $st');
    }
  }

  /// Initialize session for already logged in users
  Future<void> initializeSession(BuildContext context) async {
    if (!hasValidSession()) return;

    _isLoggedIn = true;
    notifyListeners();
    getProfile(context);
  }

  void logout() {
    _isLoggedIn = false;
    ApiManagerBase.clearToken();
    notifyListeners();
    debugPrint('AuthProvider.logout: User logged out');
  }
}
