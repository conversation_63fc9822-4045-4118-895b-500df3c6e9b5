import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_thermal_printer/utils/printer.dart';
import 'package:go_router/go_router.dart';

import '../api/api_manager.dart';
import '../common/globals.dart';
import '../common/types.dart';
import '../core/utils.dart';
import '../models/add_on_item.dart';
import '../models/base.dart';
import '../models/food/food_item.dart';
import '../models/order/order.dart';
import '../models/order/order_item.dart';
import '../models/order/order_status.dart';
import '../models/order/order_type.dart';
import '../models/restaurant/restaurant.dart';
import '../services/payment_gateway_service.dart';
import '../utils/print_utils.dart';
// Conditional imports for thermal printer
import '../utils/print_utils_web.dart' if (dart.library.io) '../utils/print_utils.dart';
import '../widgets/loading_dialog.dart';
import 'data_provider.dart';

class CartProvider extends ChangeNotifier {
  static final CartProvider _mInstance = CartProvider._internal();

  factory CartProvider() => _mInstance;

  CartProvider._internal();

  DataProvider get dataProvider => _dataProvider;
  late DataProvider _dataProvider;

  void setDataProvider(DataProvider dataProvider) {
    _dataProvider = dataProvider;

    for (CartItem item in _items) {
      item.setDataProvider(dataProvider);
    }

    notifyListeners();
  }

  final List<CartItem> _items = [];

  Iterable<CartItem> get items => _items;

  int get totalItemsCount => _items.isEmpty ? 0 : items.map((e) => e.count).reduce((value, element) => value + element);

  bool get isEmpty => _items.isEmpty;

  bool get isCartEmpty => _items.isEmpty;

  int get totalItems => totalItemsCount;

  double get totalPrice => grandTotal;
  String instructions = "";

  Map<String, Map<String, dynamic>> get cartItems {
    Map<String, Map<String, dynamic>> result = {};
    for (CartItem item in _items) {
      // Use hash as key for unique identification
      String key = item.itemHash;
      result[key] = {
        "quantity": item.count,
        "price": item.price,
        "image": item.item.imageId != null ? _dataProvider.menu.getImageUrlById(item.item.imageId!) : null,
        "cartItem": item, // Include the cart item for easy access
      };
    }
    return result;
  }

  double get grandTotal {
    double price = total;
    if (AutoChargesValues.values.isNotEmpty) {
      price += AutoChargesValues.values.map((e) => e.getCharge(total)).reduce((value, element) => value += element);
    }

    return price + tax;
  }

  double get total {
    if (items.isEmpty) return 0.0;
    try {
      return items.map((e) => e.price).reduce((value, element) => value += element);
    } catch (e) {
      debugPrint('CartProvider.total: Error calculating total: $e');
      return 0.0;
    }
  }

  double get tax {
    if (items.isEmpty) return 0.0;
    try {
      return items.map((element) => element.tax).reduce((value, element) => value += element);
    } catch (e) {
      debugPrint('CartProvider.tax: Error calculating tax: $e');
      return 0.0;
    }
  }

  OrderType? _orderType;

  OrderType? get orderType => _orderType;

  bool get isDineIn => _orderType?.isDineIn ?? false;

  void setOrderType(OrderType orderType) => _orderType = orderType;

  Iterable<CartItem> getItemsOfId(String id) => _items.where((element) => element.item.id == id);

  void setItems(Iterable<CartItem> items) {
    _items
      ..clear()
      ..addAll(items);
    notifyListeners();
  }

  int getItemCountById(String id) {
    int count = 0;
    for (CartItem item in _items.where((element) => element.id == id)) {
      count += item.count;
    }
    return count;
  }

  void addItemOfId(String id) {
    FoodItem foodItem = _dataProvider.menu.getFoodItemById(id);

    // If item has addons, always show popup for addon selection
    if (foodItem.hasAddons) {
      // Find items with this ID
      List<CartItem> itemsWithId = _items.where((element) => element.id == id).toList();

      if (itemsWithId.isEmpty) {
        // No existing items, create new one with default/must-selected addons
        CartItem newItem = CartItem(foodItem);
        newItem.setDataProvider(_dataProvider);
        _items.add(newItem);
      } else {
        // Items exist with this ID - always show popup to let user choose addons
        // This ensures user can create new variants or add to existing ones
        throw "ShowAddonPopup";
      }
    } else {
      // No addons, use simple logic
      int existingItemIndex = _items.indexWhere((element) => element.id == id);

      if (existingItemIndex != -1) {
        int? limit = foodItem.limitPerOrder;
        if (limit != null && _items[existingItemIndex].count >= limit) {
          throw "You have exceeded the limit per order";
        }
        _items[existingItemIndex].count += 1;
      } else {
        CartItem newItem = CartItem(foodItem);
        newItem.setDataProvider(_dataProvider);
        _items.add(newItem);
      }
    }
    notifyListeners();
  }

  void addItemByKey(Key key) {
    _items.firstWhere((element) => element.key == key).count += 1;
    notifyListeners();
  }

  /// Add item by hash - useful for items with addons
  void addItemByHash(String itemHash) {
    CartItem? item = _items.cast<CartItem?>().firstWhere(
      (element) => element?.itemHash == itemHash,
      orElse: () => null,
    );

    if (item != null) {
      int? limit = item.item.limitPerOrder;
      if (limit != null && item.count >= limit) {
        throw "You have exceeded the limit per order";
      }
      item.count += 1;
      notifyListeners();
    }
  }

  void addAllItems(Iterable<CartItem> items) {
    List<CartItem> filtered = [];
    for (CartItem item in items) {
      int index = filtered.indexWhere((CartItem element) {
        return element == item;
      });

      if (index != -1) {
        //  duplicate item
        filtered[index].count += item.count;
      } else {
        filtered.add(item);
      }
    }
    _items.addAll(filtered);
    notifyListeners();
  }

  void removeAllItemsOfId(String id) {
    _items.removeWhere((element) => element.item.id == id);
    notifyListeners();
  }

  void removeItemByKey(Key key) {
    int existingItemIndex = _items.indexWhere((element) => element.key == key);

    if (existingItemIndex == -1) return;

    CartItem item = _items[existingItemIndex];
    item.count -= 1;
    if (item.count < 1) {
      _items.removeAt(existingItemIndex);
    }

    notifyListeners();
  }

  void removeItemById(String id) {
    int existingItemIndex = _items.indexWhere((element) => element.id == id);

    if (existingItemIndex == -1) return;

    CartItem item = _items[existingItemIndex];
    item.count -= 1;
    if (item.count < 1) {
      _items.removeAt(existingItemIndex);
    }

    notifyListeners();
  }

  /// Remove item by hash - useful for items with addons
  void removeItemByHash(String itemHash) {
    int existingItemIndex = _items.indexWhere((element) => element.itemHash == itemHash);

    if (existingItemIndex == -1) return;

    CartItem item = _items[existingItemIndex];
    item.count -= 1;
    if (item.count < 1) {
      _items.removeAt(existingItemIndex);
    }

    notifyListeners();
  }

  // Additional methods needed by UI components
  void addCartItem(dynamic itemOrId, [double? price, String? name, int? quantity]) {
    if (itemOrId is String) {
      // Called with ID - use existing addItemOfId method
      addItemOfId(itemOrId);
    } else if (itemOrId is FoodItem) {
      // Called with FoodItem object
      addItemOfId(itemOrId.id);
    } else {
      throw ArgumentError('Invalid item type for addCartItem');
    }
  }

  void addCartItemNamePrice(String name, double price, int quantity) {
    // This is a fallback method for addon handling
    // In a proper implementation, addons should be part of the main item
    // For now, we'll create a temporary item structure
    // This should be refactored to properly handle addons
    debugPrint('Warning: addCartItemNamePrice is a temporary fallback method');
    // TODO: Implement proper addon handling
  }

  void removeCartItem(String itemHash) {
    // Find item by hash
    _items.removeWhere((element) => element.itemHash == itemHash);
    notifyListeners();
  }

  int getQuantity(String id) {
    return getItemCountById(id);
  }

  void clear() {
    _items.clear();
    notifyListeners();
  }

  /// Add item with addons to cart
  void addItemWithAddons(FoodItem foodItem, List<AddOnDelegate> selectedAddons) {
    // Create a new cart item
    CartItem cartItem = CartItem(foodItem);
    cartItem.setDataProvider(_dataProvider);

    // Clear default addons and add selected ones
    cartItem.singleAddons.clear();
    cartItem.multipleAddons.clear();

    // Add selected addons to the cart item
    for (AddOnDelegate addon in selectedAddons) {
      // Find the addon group to determine if it's single or multi-select
      bool isMultiSelect = false;
      for (AddOn addOnGroup in foodItem.addons) {
        if (addOnGroup.items.any((item) => item.refId == addon.refId)) {
          isMultiSelect = addOnGroup.multiSelect;
          break;
        }
      }

      if (isMultiSelect) {
        cartItem.multipleAddons.add(addon);
      } else {
        cartItem.singleAddons.add(addon);
      }
    }

    // Check if an identical item (same food item + same addons) already exists
    int existingItemIndex = -1;
    for (int i = 0; i < _items.length; i++) {
      if (_items[i] == cartItem) {
        existingItemIndex = i;
        break;
      }
    }

    if (existingItemIndex != -1) {
      // Increment count of existing identical item
      _items[existingItemIndex].count += 1;
    } else {
      _items.add(cartItem);
    }

    notifyListeners();
  }

  Future<void> processStripePayment(BuildContext context) async {
    LoadingDialog? loadingDialog;
    if (context.mounted) {
      loadingDialog = LoadingDialog(context, 'Placing your order...');
      loadingDialog.show();
    }
    try {
      // Validate cart and order type
      if (items.isEmpty) {
        _showErrorSnackBar(context, 'Your cart is empty');
        return;
      }

      if (_orderType == null || !_orderType!.isTakeaway) {
        _showErrorSnackBar(context, 'Please select takeaway order type');
        return;
      }

      Restaurant restaurant = _dataProvider.restaurant;

      // Check if restaurant uses Stripe
      PaymentGatewayService paymentService = PaymentGatewayService(context);
      if (!(await paymentService.isStripePaymentGateway(restaurant))) {
        if (context.mounted) {
          _showErrorSnackBar(context, 'Stripe payment not available for this restaurant');
        }
        loadingDialog?.dismiss();
        return;
      }

      // Create order request
      JSONObject orderRequest = _createOrderRequest();

      // Place takeaway order
      ApiManager apiManager = ApiManager.tokenInstance();
      Order order = await apiManager.placeOrder(orderRequest, restaurant.companyId, restaurant.revenueCenterId);

      debugPrint('CartProvider.processStripePayment: Order created: ${order.id}');

      // Hide loading
      loadingDialog?.dismiss();

      // Show loading dialog for payment initialization
      LoadingDialog? paymentInitDialog;
      if (context.mounted) {
        paymentInitDialog = LoadingDialog(context, 'Initializing payment...');
        paymentInitDialog.show();
      }
      if (order.grandTotal == 0) {
        order = await apiManager.getOrderById(order.id, restaurant.companyId, restaurant.revenueCenterId);
      }

      // Start payment process
      if (context.mounted) {
        // Hide the initialization dialog before opening webview
        paymentInitDialog?.dismiss();

        await paymentService.startTakeawayPayment(
          order: order,
          restaurant: restaurant,
          onSuccess: () => _onPaymentSuccess(context, order),
          onFailed: (error) => _onPaymentFailed(context, order, error),
        );
      }
    } catch (e, st) {
      debugPrint('CartProvider.processStripePayment: Error: $e\n$st');
      if (context.mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        _showErrorSnackBar(context, 'Failed to place order: $e');
      }
    }
  }

  /// Handle successful payment
  void _onPaymentSuccess(BuildContext context, Order order) {
    debugPrint('CartProvider._onPaymentSuccess: Payment completed for order ${order.id}');

    // Show loading dialog while processing order completion
    LoadingDialog? completionDialog;
    if (context.mounted) {
      completionDialog = LoadingDialog(context, 'Processing your order...');
      completionDialog.show();
    }

    // Add a small delay to ensure smooth transition and allow any backend processing
    Future.delayed(const Duration(milliseconds: 1500), () {
      // Hide loading dialog
      completionDialog?.dismiss();

      // Clear cart
      clear();

      // Navigate to order confirmation screen
      if (context.mounted) {
        context.push('/order-confirmation', extra: order);
      }
    });
  }

  /// Handle failed payment
  void _onPaymentFailed(BuildContext context, Order order, String error) {
    debugPrint('CartProvider._onPaymentFailed: Payment failed for order ${order.id}: $error');

    // Navigate to payment failed screen
    if (context.mounted) {
      context.push('/payment-failed', extra: {'error': error, 'order': order, 'restaurant': _dataProvider.restaurant});
    }
  }

  /// Create order request for API
  JSONObject _createOrderRequest() {
    if (items.isEmpty) {
      throw 'No items in cart';
    }

    OrderType? orderType = _orderType;
    if (orderType == null) {
      throw 'Order type not defined';
    }

    return {
      'items': items.map((item) => item.getItemMap(_dataProvider)).toList(),
      'orderTypeId': orderType.id,
      'statusId': OrderStatus.pending.id,
      'sourceId': DomainValues.guestSourceId,
      "customer": {"firstName": "Kiosk", "lastName": "User"},
    };
  }

  /// Show error snackbar
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Future<void> printBill(BuildContext context, Order order) async {
    Printer? printer = sharedPreferences.getString("printer") != null
        ? Printer.fromJson(jsonDecode(sharedPreferences.getString("printer")!))
        : null;

    if (printer == null) {
      showErrorSnackBar(context, 'No printer configured');
      return;
    }

    showSnackBar(context, "Printing bill for ${order.invoiceNumber}...");

    try {
      final apiManager = ApiManager.tokenInstance();
      final billContent = await apiManager.getPrintBillContent(order.id, order.companyId, order.revenueCenterId);

      debugPrint('🐞 CartProvider.printBill: HTML billContent length: ${billContent.length}');

      await PrintUtils.printInvoice(context: context, printer: printer, htmlString: billContent);

      if (context.mounted) {
        showSnackBar(
          context,
          'Bill Printed Successfully for ${order.invoiceNumber}, navigating to home in 3 seconds...',
        );
      }

      Future.delayed(const Duration(seconds: 3), () {
        if (context.mounted) {
          context.go('/home');
        }
      });
    } catch (e, st) {
      debugPrint("❌ Error in printBill: $e\n$st");
      if (context.mounted) {
        showErrorSnackBar(context, 'Error printing: $e');
      }
    }
  }
}

class CartItem {
  String get id => item.id;

  /// This will be unique for each item
  final Key key = UniqueKey();

  final FoodItem item;

  /// Reference to data provider for tax calculation
  DataProvider? _dataProvider;

  CartItem(this.item) : super() {
    if (!item.hasAddons) return;

    for (AddOn addOn in item.addons) {
      for (AddOnDelegate addOnDelegate in addOn.items) {
        if (addOnDelegate.defaultSelected || addOnDelegate.mustSelected) {
          if (addOn.multiSelect) {
            multipleAddons.add(addOnDelegate);
          } else {
            singleAddons.add(addOnDelegate);
          }
        }
      }
    }
  }

  /// Private constructor to set from [OrderItem]
  CartItem._order(OrderItem orderItem, DataProvider dataProvider) : item = orderItem.getItem(dataProvider), super() {
    for (AddOnDelegate addOnDelegate in orderItem.addonDelegates) {
      multipleAddons.add(addOnDelegate);
    }
  }

  factory CartItem.fromOrderItem(OrderItem orderItem, DataProvider dataProvider) =>
      CartItem._order(orderItem, dataProvider)
        ..count = orderItem.quantity
        ..orderItemId = orderItem.id
        ..kotNo = orderItem.kotNo;

  int count = 1;

  /// Id of [OrderItem] if this cart item was derived from OrderItem
  String? orderItemId;

  int? kotNo;

  double get tax {
    if (count == 0) return 0;

    // Calculate tax based on item's taxIds and menu tax values
    double totalTaxPercent = 0.0;

    // Get the data provider to access menu tax values
    // Note: This requires the cart provider to have access to data provider
    // which is set via setDataProvider method

    for (String taxId in item.taxIds) {
      // Get tax value from menu
      final taxValue = _getMenuTaxValue(taxId);
      totalTaxPercent += taxValue;
    }

    // Convert percentage to decimal and calculate tax on item price
    double itemPrice = item.price;
    if (hasAddons) {
      for (AddOnDelegate addon in addons) {
        itemPrice += addon.price;
      }
    }

    return (itemPrice * count) * (totalTaxPercent / 100.0);
  }

  double _getMenuTaxValue(String taxId) {
    if (_dataProvider?.hasMenu != true) {
      // Fallback to old tax calculation if no menu available
      return item.taxPercent.toDouble();
    }

    try {
      final tax = _dataProvider!.menu.getTaxById(taxId);
      return tax.value;
    } catch (e) {
      debugPrint('CartItem._getMenuTaxValue: Error getting tax for ID $taxId: $e');
      return 0.0;
    }
  }

  /// Set data provider for tax calculation
  void setDataProvider(DataProvider dataProvider) {
    _dataProvider = dataProvider;
  }

  double get price {
    if (count == 0) return 0;

    double price = item.price;
    if (hasAddons) {
      for (AddOnDelegate addon in addons) {
        price += addon.price;
      }
    }

    return price * count;
  }

  List<AddOnDelegate> singleAddons = [];
  List<AddOnDelegate> multipleAddons = [];

  List<AddOnDelegate> get addons => [...singleAddons, ...multipleAddons];

  bool get hasAddons => addons.isNotEmpty;

  bool get hasSingleAddons => singleAddons.isNotEmpty;

  bool get hasMultipleAddons => multipleAddons.isNotEmpty;

  String getAddonsFormattedText(DataProvider dataProvider) {
    if (!hasAddons) return "";

    List<String> addonNames = addons.map((e) => e.getItem(dataProvider).name).toList();
    if (addonNames.isEmpty) return "";

    // Format as "With: addon1, addon2, addon3"
    return "With: ${addonNames.join(", ")}";
  }

  String? choiceId;

  List<String> customizationIds = [];

  bool get hasChoice => choiceId != null;

  bool get hasCustomizations => customizationIds.isNotEmpty;

  JSONObject getItemMap(DataProvider dataProvider, {OrderStatus status = OrderStatus.pending, String? instructions}) {
    JSONObject map = {
      "inventoryId": id,
      "inventoryName": item.name,
      "addons": addons.map((e) => e.getRequestMap(dataProvider)).toList(),
      "quantity": count,
      "price": item.price,
      "statusId": status.id,
      "totalPrice": price,
      'taxId': item.taxIds,
    };

    if (kotNo != null) map["kotNumber"] = kotNo;
    if (instructions != null) map["kotInstructions"] = instructions;

    return map;
  }

  String toStringWithProvider(DataProvider dataProvider) => "${item.id},${getAddonsFormattedText(dataProvider)}";

  @override
  String toString() => "${item.id},[${addons.length} addons] ${item.name}";

  /// Generate a unique hash for this cart item including addons
  String get itemHash {
    if (!hasAddons) {
      return item.id;
    }

    // Sort addons by refId to ensure consistent hash for same addon combinations
    List<String> addonIds = addons.map((addon) => addon.refId).toList()..sort();
    return "${item.id}_${addonIds.join('_')}";
  }

  @override
  int get hashCode {
    if (!hasAddons) {
      return item.id.hashCode;
    }

    // Create hash based on item ID and sorted addon IDs
    List<String> addonIds = addons.map((addon) => addon.refId).toList()..sort();
    return "${item.id}_${addonIds.join('_')}".hashCode;
  }

  @override
  bool operator ==(Object other) {
    if (other is CartItem) {
      if (item.id != other.item.id) return false;

      // If neither has addons, they're equal
      if (!hasAddons && !other.hasAddons) return true;

      // If one has addons and other doesn't, they're not equal
      if (hasAddons != other.hasAddons) return false;

      // Both have addons - compare addon sets
      if (addons.length != other.addons.length) return false;

      Set<String> thisAddonIds = addons.map((addon) => addon.refId).toSet();
      Set<String> otherAddonIds = other.addons.map((addon) => addon.refId).toSet();

      return thisAddonIds.containsAll(otherAddonIds) && otherAddonIds.containsAll(thisAddonIds);
    }
    return false;
  }
}
