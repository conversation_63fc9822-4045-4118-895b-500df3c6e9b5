import '../common/types.dart';

typedef EmvList = List<Emv>;
typedef EmvIterable = Iterable<Emv>;

class Emv{
  final JSONObject json;
  Emv(this.json);

  String get id => json["id"];

  int get lastSeenAt => json["last_seen_at"];

  String get deviceType => json["device_type"];

  String get label => json["label"];

  String get status => json["status"];

  String get serialNumber => json["serial_number"];

}