import 'package:flutter/material.dart';
import 'package:flutter_thermal_printer/flutter_thermal_printer.dart';
import 'package:flutter_thermal_printer/utils/printer.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';

class PrintUtils {
  static String preprocessHtml(String html) {
    html = html.replaceAllMapped(
      RegExp(r'p,\s*span\s*{[^}]*font-size:[^;]*!important[^}]*}'),
          (match) => match.group(0)!.replaceAll('!important', ''),
    );
    return html;
  }

  static String addFlexDirectionToParentDiv(String html) {
    final regex = RegExp(r'(<div[^>]*flex-wrap[^>]*>.*?Taxes.*?</div>)', dotAll: true);
    return html.replaceAllMapped(regex, (match) {
      String divContent = match.group(0)!;

      if (divContent.contains('style="')) {
        divContent = divContent.replaceFirstMapped(RegExp(r'style="([^"]*)"'), (styleMatch) {
          final existingStyles = styleMatch.group(1)!;
          if (existingStyles.contains('flex-direction')) {
            return 'style="$existingStyles"';
          }
          return 'style="$existingStyles; display:flex; flex-direction:column; margin-bottom:5px;"';
        });
      } else {
        divContent = divContent.replaceFirst(
          '<div',
          '<div style="display:flex; flex-direction:column; margin-bottom:5px;"',
        );
      }

      return divContent;
    });
  }

  static Future<void> printInvoice({
    required BuildContext context,
    required Printer printer,
    required String htmlString,
    bool cutAfterPrint = true,
  }) async {
    // Preprocess HTML to remove !important
    final processedHtml = preprocessHtml(htmlString);

    // Extract the image URL from HTML
    final imageRegex = RegExp(
      r'''<img\b[^>]*\bsrc\s*=\s*["']([^"']+)["'][^>]*>''',
      caseSensitive: false,
    );
    final match = imageRegex.firstMatch(processedHtml);
    String? imageUrl;
    if (match != null) {
      imageUrl = match.group(1);
    }

    // Precache the image to ensure it loads before printing
    if (imageUrl != null && imageUrl.isNotEmpty) {
      final imageProvider = NetworkImage(imageUrl);
      await precacheImage(imageProvider, context);
    }

    await FlutterThermalPrinter.instance.printWidget(
      context,
      printer: printer,
      widget: Container(
        width: 450,
        color: Colors.white,
        child: Padding(
          padding: const EdgeInsets.only(bottom: 80.0),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1.6)),
            child: Directionality(
              textDirection: TextDirection.ltr,
              child: SingleChildScrollView(
                child: HtmlWidget(
                  addFlexDirectionToParentDiv(processedHtml),
                  textStyle: const TextStyle(color: Colors.black),
                  customStylesBuilder: (element) {
                    if (element.localName == 'p' || element.localName == 'span') {
                      return {
                        'font-size': '10px',
                        'padding': '0',
                        'margin': '0',
                      };
                    }
                    return null;
                  },
                  customWidgetBuilder: (element) {
                    if (element.localName == 'img') {
                      final src = element.attributes['src'];
                      final style = element.attributes['style'] ?? '';
                      double width = 90.0;
                      double height = 50.0;
                      final widthMatch = RegExp(r'width\s*:\s*(\d+)').firstMatch(style);
                      final heightMatch = RegExp(r'height\s*:\s*(\d+)').firstMatch(style);
                      if (widthMatch != null) width = double.parse(widthMatch.group(1)!);
                      if (heightMatch != null) height = double.parse(heightMatch.group(1)!);
                      if (src != null) {
                        return Image.network(
                          src,
                          width: width,
                          height: height,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: width,
                              height: height,
                              color: Colors.grey[300],
                              child: const Icon(Icons.image_not_supported, color: Colors.grey),
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return SizedBox(
                              width: width,
                              height: height,
                              child: const Center(child: CircularProgressIndicator()),
                            );
                          },
                        );
                      }
                    }
                    if (element.localName == 'div' && element.attributes['style']?.contains('border-top') == true) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Divider(color: Colors.black, height: 1, thickness: 1, indent: 0, endIndent: 0),
                      );
                    }
                    if (element.localName == 'div' &&
                        element.attributes['style']?.contains('display: flex') == true &&
                        element.attributes['style']?.contains('align-items: center') == true) {
                      final children = element.children;
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 5,
                            child: Text(
                              children.isNotEmpty ? children[0].text.trim() : '',
                              style: const TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              children.length > 1 ? children[1].text.trim() : '',
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ),
                          Expanded(
                            flex: 4,
                            child: Text(
                              children.length > 2 ? children[2].text.trim() : '',
                              textAlign: TextAlign.right,
                              style: const TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ),
                        ],
                      );
                    }
                    return null;
                  },
                ),
              ),
            ),
          ),
        ),
      ),
      cutAfterPrinted: cutAfterPrint,
    );
  }
}